<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SettingController extends Controller
{
    public function index()
    {
        try {
            $settings = Setting::where('group', 'accounting')->get();
            return view('accountant.settings.index', compact('settings'));
        } catch (\Exception $e) {
            Log::error('Erreur dans SettingController@index: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement des paramètres.');
        }
    }

    public function update(Request $request)
    {
        try {
            $validated = $request->validate([
                'settings' => 'required|array',
                'settings.*' => 'required|string'
            ]);

            foreach ($validated['settings'] as $key => $value) {
                Setting::updateOrCreate(
                    ['key' => $key, 'group' => 'accounting'],
                    ['value' => $value]
                );
            }

            return back()->with('success', 'Les paramètres ont été mis à jour avec succès.');
        } catch (\Exception $e) {
            Log::error('Erreur dans SettingController@update: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la mise à jour des paramètres.')
                ->withInput();
        }
    }
}
