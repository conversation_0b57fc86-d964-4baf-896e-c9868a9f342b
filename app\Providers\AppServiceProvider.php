<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Définir le template de pagination par défaut
        \Illuminate\Pagination\Paginator::useBootstrap();
        
        // Définir la langue par défaut
        App::setLocale('fr');
        
        Schema::defaultStringLength(191);

        // Utiliser les variables d'environnement pour la configuration de la base de données
        // Cela permet d'utiliser les paramètres du fichier .env
        Config::set('database.connections.mysql.host', env('DB_HOST', '127.0.0.1'));
        Config::set('database.connections.mysql.database', env('DB_DATABASE', 'gradis_db'));
        Config::set('database.connections.mysql.username', env('DB_USERNAME', 'root'));
        Config::set('database.connections.mysql.password', env('DB_PASSWORD', ''));

        // Forcer la reconnexion pour appliquer les changements
        DB::purge('mysql');
        DB::reconnect('mysql');
    }
}
