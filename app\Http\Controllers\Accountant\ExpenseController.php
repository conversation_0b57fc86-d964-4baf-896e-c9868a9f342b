<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Expense;
use App\Models\ExpenseCategory;
use App\Models\Supplier;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Support\Str;

class ExpenseController extends Controller
{
    /**
     * Affiche la liste des dépenses
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        try {
            $query = Expense::query();
            
            // Filtrage par catégorie
            if ($request->has('category') && $request->category != 'all') {
                $query->where('category_id', $request->category);
            }
            
            // Filtrage par date
            if ($request->has('start_date') && $request->has('end_date')) {
                $query->whereBetween('date', [$request->start_date, $request->end_date]);
            }
            
            // Filtrage par statut
            if ($request->has('status') && $request->status != 'all') {
                $query->where('status', $request->status);
            }
            
            // Filtrage par montant
            if ($request->has('min_amount') && $request->has('max_amount')) {
                $query->whereBetween('amount', [$request->min_amount, $request->max_amount]);
            }
            
            // Tri
            $sort = $request->get('sort', 'date');
            $direction = $request->get('direction', 'desc');
            $query->orderBy($sort, $direction);
            
            // Pagination avec 15 éléments par page
            $expenses = $query->paginate(15);
            
            // Récupérer toutes les catégories de dépenses pour le filtrage
            $categories = ExpenseCategory::all();
            
            // Calculer les statistiques
            $totalExpenses = Expense::sum('amount');
            $paidExpenses = Expense::where('status', 'paid')->sum('amount');
            $pendingExpenses = Expense::where('status', 'pending')->sum('amount');
            
            // Récupérer les dépenses par catégorie pour les graphiques
            $expensesByCategory = DB::table('expenses')
                ->join('expense_categories', 'expenses.category_id', '=', 'expense_categories.id')
                ->select('expense_categories.name as category', DB::raw('SUM(expenses.amount) as total'))
                ->groupBy('expense_categories.name')
                ->get();
            
            // Récupérer les dépenses par mois pour les graphiques
            $expensesByMonth = DB::table('expenses')
                ->select(DB::raw('MONTH(date) as month'), DB::raw('YEAR(date) as year'), DB::raw('SUM(amount) as total'))
                ->groupBy('year', 'month')
                ->orderBy('year', 'asc')
                ->orderBy('month', 'asc')
                ->get();
            
            // Formater les données pour les graphiques
            $chartData = [
                'categories' => $expensesByCategory->pluck('category')->toJson(),
                'amounts' => $expensesByCategory->pluck('total')->toJson(),
                'months' => $expensesByMonth->map(function($item) {
                    $monthName = Carbon::create(null, $item->month, 1)->format('M');
                    return $monthName . ' ' . $item->year;
                })->toJson(),
                'monthlyAmounts' => $expensesByMonth->pluck('total')->toJson()
            ];
            
            return view('accountant.expenses.index', compact(
                'expenses', 
                'categories', 
                'totalExpenses', 
                'paidExpenses', 
                'pendingExpenses', 
                'chartData'
            ));
        } catch (\Exception $e) {
            Log::error('Erreur dans ExpenseController@index: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement des dépenses.');
        }
    }

    /**
     * Affiche le formulaire de création d'une dépense
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        try {
            $categories = ExpenseCategory::all();
            $suppliers = Supplier::all();
            
            return view('accountant.expenses.create', compact('categories', 'suppliers'));
        } catch (\Exception $e) {
            Log::error('Erreur dans ExpenseController@create: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement du formulaire.');
        }
    }

    /**
     * Enregistre une nouvelle dépense
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Validation des données
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'amount' => 'required|numeric|min:0',
            'date' => 'required|date',
            'category_id' => 'required|exists:expense_categories,id',
            'supplier_id' => 'nullable|exists:suppliers,id',
            'reference_number' => 'nullable|string|max:50',
            'payment_method' => 'required|string|in:cash,bank_transfer,check,credit_card',
            'status' => 'required|string|in:paid,pending,cancelled',
            'receipt_image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        try {
            DB::beginTransaction();

            // Générer un numéro de référence si non fourni
            if (!$request->reference_number) {
                $reference_number = 'EXP-' . date('Ymd') . '-' . rand(1000, 9999);
            } else {
                $reference_number = $request->reference_number;
            }

            // Gérer l'upload du reçu si fourni
            $receipt_path = null;
            if ($request->hasFile('receipt_image')) {
                $receipt_path = $request->file('receipt_image')->store('receipts', 'public');
            }

            // Créer la dépense
            $expense = Expense::create([
                'title' => $request->title,
                'description' => $request->description,
                'amount' => $request->amount,
                'date' => $request->date,
                'category_id' => $request->category_id,
                'supplier_id' => $request->supplier_id,
                'reference_number' => $reference_number,
                'payment_method' => $request->payment_method,
                'status' => $request->status,
                'receipt_image' => $receipt_path,
                'user_id' => Auth::id(),
            ]);

            DB::commit();

            return redirect()->route('accountant.expenses.show', $expense)
                ->with('success', 'La dépense a été créée avec succès.');
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Erreur dans ExpenseController@store: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de l\'enregistrement de la dépense: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Affiche les détails d'une dépense
     *
     * @param Expense $expense
     * @return \Illuminate\View\View
     */
    public function show(Expense $expense)
    {
        try {
            return view('accountant.expenses.show', compact('expense'));
        } catch (\Exception $e) {
            Log::error('Erreur dans ExpenseController@show: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement de la dépense.');
        }
    }

    /**
     * Affiche le formulaire d'édition d'une dépense
     *
     * @param Expense $expense
     * @return \Illuminate\View\View
     */
    public function edit(Expense $expense)
    {
        try {
            $categories = ExpenseCategory::all();
            $suppliers = Supplier::all();
            
            return view('accountant.expenses.edit', compact('expense', 'categories', 'suppliers'));
        } catch (\Exception $e) {
            Log::error('Erreur dans ExpenseController@edit: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement du formulaire d\'édition.');
        }
    }

    /**
     * Met à jour une dépense
     *
     * @param Request $request
     * @param Expense $expense
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Expense $expense)
    {
        // Validation des données
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'amount' => 'required|numeric|min:0',
            'date' => 'required|date',
            'category_id' => 'required|exists:expense_categories,id',
            'supplier_id' => 'nullable|exists:suppliers,id',
            'reference_number' => 'nullable|string|max:50',
            'payment_method' => 'required|string|in:cash,bank_transfer,check,credit_card',
            'status' => 'required|string|in:paid,pending,cancelled',
            'receipt_image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        try {
            DB::beginTransaction();

            // Gérer l'upload du reçu si un nouveau fichier est fourni
            $receipt_path = $expense->receipt_image;
            if ($request->hasFile('receipt_image')) {
                $receipt_path = $request->file('receipt_image')->store('receipts', 'public');
            }

            // Mettre à jour la dépense
            $expense->update([
                'title' => $request->title,
                'description' => $request->description,
                'amount' => $request->amount,
                'date' => $request->date,
                'category_id' => $request->category_id,
                'supplier_id' => $request->supplier_id,
                'reference_number' => $request->reference_number,
                'payment_method' => $request->payment_method,
                'status' => $request->status,
                'receipt_image' => $receipt_path,
                'updated_by' => Auth::id(),
            ]);

            DB::commit();

            return redirect()->route('accountant.expenses.show', $expense)
                ->with('success', 'La dépense a été mise à jour avec succès.');
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Erreur dans ExpenseController@update: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la mise à jour de la dépense: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Supprime une dépense
     *
     * @param Expense $expense
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Expense $expense)
    {
        try {
            DB::beginTransaction();
            
            $expense->delete();
            
            DB::commit();
            
            return redirect()->route('accountant.expenses.index')
                ->with('success', 'La dépense a été supprimée avec succès.');
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Erreur dans ExpenseController@destroy: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la suppression de la dépense.');
        }
    }

    /**
     * Affiche les dépenses par catégorie
     *
     * @param ExpenseCategory $category
     * @return \Illuminate\View\View
     */
    public function byCategory(ExpenseCategory $category)
    {
        try {
            $expenses = Expense::where('category_id', $category->id)->paginate(15);
            
            return view('accountant.expenses.by-category', compact('expenses', 'category'));
        } catch (\Exception $e) {
            Log::error('Erreur dans ExpenseController@byCategory: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement des dépenses par catégorie.');
        }
    }

    /**
     * Génère un PDF pour une dépense
     *
     * @param Expense $expense
     * @return \Illuminate\Http\Response
     */
    public function print(Expense $expense)
    {
        try {
            $pdf = PDF::loadView('accountant.expenses.print', compact('expense'));
            
            return $pdf->stream('depense-' . $expense->reference_number . '.pdf');
        } catch (\Exception $e) {
            Log::error('Erreur dans ExpenseController@print: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la génération du PDF: ' . $e->getMessage());
        }
    }
}
