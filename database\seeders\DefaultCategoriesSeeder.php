<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;
use Illuminate\Support\Str;

class DefaultCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Ciment',
                'description' => 'Catégorie pour les produits de ciment',
                'type' => 'cement',
                'is_active' => true
            ],
            [
                'name' => 'Fer',
                'description' => 'Catégorie pour les produits en fer',
                'type' => 'iron',
                'is_active' => true
            ],
            [
                'name' => 'Matériaux de construction',
                'description' => 'Catégorie pour les matériaux de construction divers',
                'type' => 'standard',
                'is_active' => true
            ]
        ];

        foreach ($categories as $category) {
            Category::firstOrCreate(
                ['name' => $category['name']],
                array_merge($category, ['slug' => Str::slug($category['name'])])
            );
        }
    }
}
