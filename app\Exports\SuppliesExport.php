<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class SuppliesExport implements FromCollection, WithHeadings, WithStyles, WithTitle
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        $collection = collect();
        
        // Statistiques
        $collection->push([
            'STATISTIQUES GÉNÉRALES', '', '', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Total des approvisionnements', number_format($this->data['stats']['total_supplies']), '', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Montant total', number_format($this->data['stats']['total_amount'], 0, ',', ' ') . ' FCFA', '', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Tonnage total', number_format($this->data['stats']['total_tonnage'], 2) . ' T', '', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Montant validé', number_format($this->data['stats']['validated_amount'], 0, ',', ' ') . ' FCFA', '', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Montant en attente', number_format($this->data['stats']['pending_amount'], 0, ',', ' ') . ' FCFA', '', '', '', '', '', '', ''
        ]);
        $collection->push(['', '', '', '', '', '', '', '', '']); // Ligne vide

        // En-têtes des approvisionnements
        $collection->push([
            'LISTE DES APPROVISIONNEMENTS', '', '', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Date', 'Référence', 'Fournisseur', 'Catégorie', 'Tonnage (T)', 'Montant (FCFA)', 'Statut', 'Créé par', 'Date livraison'
        ]);

        // Données des approvisionnements
        foreach ($this->data['supplies'] as $supply) {
            $collection->push([
                $supply->created_at->format('d/m/Y H:i'),
                $supply->reference,
                $supply->supplier ? $supply->supplier->name : 'N/A',
                $supply->category ? $supply->category->name : 'N/A',
                number_format($supply->total_tonnage, 2),
                number_format($supply->total_amount, 0, ',', ' '),
                $this->getStatusText($supply->status),
                $supply->createdBy ? $supply->createdBy->name : 'N/A',
                $supply->expected_delivery_date ? $supply->expected_delivery_date->format('d/m/Y') : 'N/A'
            ]);
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'RAPPORT DES APPROVISIONNEMENTS',
            'Période: ' . \Carbon\Carbon::parse($this->data['start_date'])->format('d/m/Y') . ' - ' . \Carbon\Carbon::parse($this->data['end_date'])->format('d/m/Y'),
            '',
            '',
            '',
            '',
            '',
            '',
            ''
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 16,
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => 'FF858796',
                    ],
                ],
            ],
            2 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],
        ];
    }

    public function title(): string
    {
        return 'Approvisionnements';
    }

    private function getStatusText($status)
    {
        switch ($status) {
            case 'validated':
                return 'Validé';
            case 'pending':
                return 'En attente';
            case 'rejected':
                return 'Rejeté';
            default:
                return $status;
        }
    }
}
