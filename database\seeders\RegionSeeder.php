<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Region;
use App\Models\City;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class RegionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Disable foreign key checks temporarily
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Clean the tables
        City::truncate();
        Region::truncate();

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1');

        // Régions et villes du Togo
        $regions = [
            'Maritime' => [
                'Lomé', 'Tsévié', 'Aného', 'Vogan', 'Tabligbo', 'Afagnagan'
            ],
            'Plateaux' => [
                'Atakpamé', 'Kpalimé', 'Badou', 'Notsé', 'Amlamé', 'Danyi'
            ],
            'Centrale' => [
                'Sokodé', 'Tchamba', 'Sotouboua', '<PERSON><PERSON><PERSON>'
            ],
            'Kara' => [
                '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Niamtoug<PERSON>', 'Pagouda'
            ],
            'Savan<PERSON>' => [
                'Da<PERSON><PERSON>', 'Mango', 'Tandjoaré', 'Cinkassé'
            ]
        ];

        foreach ($regions as $regionName => $cities) {
            $region = Region::create([
                'name' => $regionName,
                'slug' => Str::slug($regionName),
                'country' => 'Togo'
            ]);

            foreach ($cities as $cityName) {
                City::create([
                    'name' => $cityName,
                    'slug' => Str::slug($cityName),
                    'region_id' => $region->id,
                    'is_active' => true
                ]);
            }
        }
    }
}
