<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('iron_specifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->integer('diameter')->comment('Diamètre en millimètres (6, 8, 10, 12, 14, 16)');
            $table->decimal('unit_price', 10, 2)->comment('Prix à l\'unité pour la vente en détail');
            $table->decimal('ton_price', 10, 2)->comment('Prix par tonne pour la vente en gros');
            $table->integer('units_per_ton')->comment('Nombre d\'unités nécessaires pour constituer une tonne');
            $table->decimal('length', 8, 2)->default(12)->comment('Longueur standard en mètres');
            $table->decimal('weight_per_unit', 8, 3)->comment('Poids par unité en kilogrammes');
            $table->text('description')->nullable();
            $table->timestamps();

            // Garantir qu'un produit n'a qu'une seule spécification par diamètre
            $table->unique(['product_id', 'diameter']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('iron_specifications');
    }
};
