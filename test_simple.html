<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple - Exports</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-btn {
            display: block;
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        .test-btn:hover {
            background: #0056b3;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Simple des Exports</h1>
        
        <button class="test-btn" onclick="testRoute()">
            🖥️ Tester Serveur Laravel
        </button>
        
        <button class="test-btn" onclick="testDirectExport('weekly')">
            📅 Tester Rapport Hebdomadaire
        </button>
        
        <button class="test-btn" onclick="testDirectExport('monthly')">
            📆 Tester Rapport Mensuel
        </button>
        
        <button class="test-btn" onclick="testDirectExport('performance')">
            📈 Tester Rapport Performance
        </button>
        
        <div id="result" class="result"></div>
        
        <div id="log" class="log">
            <div>🚀 Interface de test initialisée</div>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function showResult(message, type) {
            const result = document.getElementById('result');
            result.className = `result ${type}`;
            result.textContent = message;
            result.style.display = 'block';
            
            setTimeout(() => {
                result.style.display = 'none';
            }, 5000);
        }
        
        async function testRoute() {
            log('🔄 Test de la route serveur...');

            try {
                const response = await fetch('http://127.0.0.1:8000/test-server');
                const data = await response.json();

                if (data.success) {
                    log('✅ Serveur Laravel OK');
                    showResult('Serveur Laravel fonctionne !', 'success');
                    console.log('Données:', data);
                } else {
                    log('❌ Serveur Laravel échoué');
                    showResult('Erreur serveur Laravel', 'error');
                }
            } catch (error) {
                log('❌ Erreur: ' + error.message);
                showResult('Erreur de connexion au serveur', 'error');
            }
        }
        
        async function testDirectExport(type) {
            log(`🔄 Test export simple: ${type}...`);

            try {
                const response = await fetch('http://127.0.0.1:8000/test-export-simple', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ type: type })
                });

                const data = await response.json();

                if (data.success) {
                    log(`✅ Export ${type} OK`);
                    showResult(`Export ${type} réussi !`, 'success');
                    console.log('Données du rapport:', data);
                } else {
                    log(`❌ Export ${type} échoué: ${data.message}`);
                    showResult(`Erreur export ${type}`, 'error');
                }
            } catch (error) {
                log(`❌ Erreur export ${type}: ${error.message}`);
                showResult(`Erreur export ${type}`, 'error');
            }
        }
        
        log('🎯 Interface prête - Cliquez sur les boutons pour tester');
    </script>
</body>
</html>
