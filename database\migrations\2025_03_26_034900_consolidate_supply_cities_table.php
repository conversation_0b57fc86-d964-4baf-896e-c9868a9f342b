<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        // Désactiver les contraintes de clé étrangère
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Supprimer les anciennes tables si elles existent
        if (Schema::hasTable('supply_city')) {
            Schema::dropIfExists('supply_city');
        }

        // Modifier la table supply_cities si elle existe
        if (Schema::hasTable('supply_cities')) {
            Schema::table('supply_cities', function (Blueprint $table) {
                // Supprimer toutes les contraintes de clé étrangère existantes
                $foreignKeys = Schema::getConnection()
                    ->getDoctrineSchemaManager()
                    ->listTableForeignKeys('supply_cities');

                foreach ($foreignKeys as $foreignKey) {
                    $table->dropForeign($foreignKey->getName());
                }

                // Supprimer les colonnes existantes
                $table->dropColumn([
                    'supply_id',
                    'city_id',
                    'vehicle_id',
                    'driver_id',
                    'quantity',
                    'remaining_quantity',
                    'price',
                    'trips',
                    'remaining_trips'
                ]);
            });

            // Ajouter les nouvelles colonnes
            Schema::table('supply_cities', function (Blueprint $table) {
                $table->foreignId('supply_id')->constrained()->onDelete('cascade');
                $table->foreignId('city_id')->constrained();
                $table->foreignId('vehicle_id')->constrained('trucks');
                $table->foreignId('driver_id')->nullable()->constrained('drivers');
                $table->decimal('quantity', 15, 2)->comment('Quantité totale en tonnes');
                $table->decimal('remaining_quantity', 15, 2)->nullable()->comment('Quantité restante en tonnes');
                $table->decimal('price', 15, 2)->comment('Prix unitaire');
                $table->integer('trips')->default(1)->comment('Nombre total de voyages');
                $table->integer('remaining_trips')->nullable()->comment('Nombre de voyages restants');
            });
        } else {
            // Créer la table si elle n'existe pas
            Schema::create('supply_cities', function (Blueprint $table) {
                $table->id();
                $table->foreignId('supply_id')->constrained()->onDelete('cascade');
                $table->foreignId('city_id')->constrained();
                $table->foreignId('vehicle_id')->constrained('trucks');
                $table->foreignId('driver_id')->nullable()->constrained('drivers');
                $table->decimal('quantity', 15, 2)->comment('Quantité totale en tonnes');
                $table->decimal('remaining_quantity', 15, 2)->nullable()->comment('Quantité restante en tonnes');
                $table->decimal('price', 15, 2)->comment('Prix unitaire');
                $table->integer('trips')->default(1)->comment('Nombre total de voyages');
                $table->integer('remaining_trips')->nullable()->comment('Nombre de voyages restants');
                $table->timestamps();
                $table->softDeletes();
            });
        }

        // Réactiver les contraintes de clé étrangère
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    public function down()
    {
        // Rien à faire ici car c'est une migration de consolidation
    }
};
