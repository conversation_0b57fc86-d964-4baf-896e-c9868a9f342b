<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('regions', function (Blueprint $table) {
            $table->string('country')->default('Togo')->after('name');
        });

        // Mettre à jour les régions existantes
        DB::table('regions')->whereIn('name', [
            'Maritime',
            'Plateaux',
            'Centrale',
            'Kara',
            'Savanes'
        ])->update(['country' => 'Togo']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('regions', function (Blueprint $table) {
            $table->dropColumn('country');
        });
    }
};
