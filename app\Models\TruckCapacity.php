<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TruckCapacity extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'capacity',
        'unit',
        'description',
        'is_active'
    ];

    protected $casts = [
        'capacity' => 'decimal:2',
        'is_active' => 'boolean'
    ];

    public function trucks()
    {
        return $this->hasMany(Truck::class);
    }
}
