<?php

namespace App\Http\Controllers\CementManager;

use App\Http\Controllers\Controller;
use App\Models\CementOrder;
use App\Models\CementOrderDetail;
use App\Models\Driver;
use App\Models\Truck;
use App\Models\TripAssignment;
use Illuminate\Http\Request;

class AssignmentController extends Controller
{
    public function index()
    {
        $assignments = TripAssignment::with(['cementOrder', 'driver', 'truck'])
            ->latest()
            ->paginate(10);

        return view('cement-manager.assignments.index', compact('assignments'));
    }

    public function create(CementOrder $order)
    {
        $order->load(['details.product', 'details.destination', 'details.customer']);
        $trucks = Truck::with('capacity')->available()->get();
        $drivers = Driver::where('status', 'available')->get();
        $existingAssignments = TripAssignment::where('cement_order_id', $order->id)
            ->with(['driver', 'truck.capacity', 'cementOrderDetail'])
            ->get();

        return view('cement-manager.assignments.create', compact('order', 'trucks', 'drivers', 'existingAssignments'));
    }

    public function store(Request $request, CementOrder $order)
    {
        try {
            $validated = $request->validate([
                'cement_order_detail_id' => 'required|exists:cement_order_details,id',
                'truck_id' => 'required|exists:trucks,id',
                'driver_id' => 'required|exists:drivers,id',
                'trip_number' => 'required|integer|min:1',
                'start_date' => 'required|date_format:Y-m-d\TH:i',
                'end_date' => 'required|date_format:Y-m-d\TH:i|after:start_date',
                'notes' => 'nullable|string',
            ]);

            // Récupérer le détail de la commande
            $detail = CementOrderDetail::findOrFail($validated['cement_order_detail_id']);
            
            // Créer l'affectation
            $assignment = new TripAssignment($validated);
            $assignment->cement_order_id = $order->id;
            $assignment->tonnage = $detail->tonnage_per_trip;
            $assignment->status = 'pending';
            $assignment->save();

            // Mettre à jour le statut du camion et du chauffeur
            $truck = Truck::find($validated['truck_id']);
            $driver = Driver::find($validated['driver_id']);
            
            $truck->update(['status' => 'busy']);
            $driver->update(['status' => 'busy']);

            return redirect()->route('cement-manager.assignments.index')
                ->with('success', 'Affectation créée avec succès');

        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->withErrors(['error' => 'Une erreur est survenue lors de la création de l\'affectation : ' . $e->getMessage()]);
        }
    }

    public function updateStatus(Request $request, TripAssignment $assignment)
    {
        try {
            $validated = $request->validate([
                'status' => 'required|in:pending,completed'
            ]);

            $assignment->update([
                'status' => $validated['status'],
                'completed_at' => $validated['status'] === 'completed' ? now() : null
            ]);

            // Si l'affectation est terminée, libérer le camion et le chauffeur
            if ($validated['status'] === 'completed') {
                $assignment->truck->update(['status' => 'available']);
                $assignment->driver->update(['status' => 'available']);
            }

            return back()->with('success', 'Statut de l\'affectation mis à jour avec succès');
        } catch (\Exception $e) {
            return back()->with('error', 'Erreur lors de la mise à jour du statut : ' . $e->getMessage());
        }
    }
}
