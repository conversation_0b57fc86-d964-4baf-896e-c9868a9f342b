<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('supplies', function (Blueprint $table) {
            // Supprimer la contrainte de clé étrangère sur supplier_id
            $table->dropForeign(['supplier_id']);
            $table->dropColumn('supplier_id');
            
            // Ajouter la colonne supplier comme chaîne de caractères
            $table->string('supplier')->after('reference');
        });
    }

    public function down()
    {
        Schema::table('supplies', function (Blueprint $table) {
            $table->dropColumn('supplier');
            $table->foreignId('supplier_id')->constrained();
        });
    }
};
