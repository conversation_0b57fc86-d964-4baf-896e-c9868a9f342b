<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Role extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'slug'];

    public const ADMIN = 'admin';
    public const ACCOUNTANT = 'accountant';
    public const CEMENT_MANAGER = 'cement_manager';
    public const IRON_MANAGER = 'iron_manager';
    public const CASHIER = 'cashier';
    public const CUSTOMER_SERVICE = 'customer_service';
    public const CUSTOMER = 'customer';

    public function users()
    {
        return $this->belongsToMany(User::class);
    }
}
