<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use <PERSON><PERSON>\Permission\Models\Permission;

class RoleAndPermissionSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Créer les permissions
        $permissions = [
            'view_users',
            'create_users',
            'edit_users',
            'delete_users',
            'view_trucks',
            'create_trucks',
            'edit_trucks',
            'delete_trucks',
            'view_assignments',
            'create_assignments',
            'edit_assignments',
            'delete_assignments',
            'view_orders',
            'create_orders',
            'edit_orders',
            'delete_orders',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Créer les rôles et assigner les permissions
        $roles = [
            'super-admin' => $permissions,
            'admin' => $permissions,
            'accountant' => [
                'view_trucks', 'create_trucks', 'edit_trucks', 'delete_trucks',
                'view_assignments', 'create_assignments', 'edit_assignments', 'delete_assignments'
            ],
            'cement-manager' => [
                'view_orders', 'create_orders', 'edit_orders', 'delete_orders',
                'view_assignments', 'create_assignments'
            ],
            'driver' => [
                'view_assignments'
            ]
        ];

        foreach ($roles as $role => $rolePermissions) {
            $createdRole = Role::create(['name' => $role]);
            $createdRole->givePermissionTo($rolePermissions);
        }
    }
}
