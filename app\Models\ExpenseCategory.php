<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExpenseCategory extends Model
{
    use HasFactory;
    
    /**
     * Les attributs qui sont assignables en masse.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'color_code',
        'icon',
    ];

    /**
     * Relation avec les dépenses
     */
    public function expenses()
    {
        return $this->hasMany(Expense::class, 'category_id');
    }
    
    /**
     * Récupère le montant total des dépenses pour cette catégorie
     */
    public function getTotalExpensesAttribute()
    {
        return $this->expenses()->sum('amount');
    }
}
