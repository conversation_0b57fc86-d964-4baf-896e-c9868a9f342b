<?php

namespace App\Http\Controllers;

use App\Models\CementOrder;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CementOrderStatsController extends Controller
{
    public function index()
    {
        try {
            $stats = [
                'total_cement_orders' => CementOrder::where('created_by', auth()->id())->count(),
                'pending_cement_orders' => CementOrder::where('created_by', auth()->id())
                    ->where('status', 'pending')
                    ->count(),
                'total_cement_tonnage' => CementOrder::where('created_by', auth()->id())
                    ->sum('total_tonnage'),
                'monthly_cement_orders' => CementOrder::where('created_by', auth()->id())
                    ->whereMonth('created_at', Carbon::now()->month)
                    ->count()
            ];

            // Données pour le graphique des commandes par jour
            $dailyOrders = CementOrder::where('created_by', auth()->id())
                ->whereBetween('created_at', [Carbon::now()->subDays(6), Carbon::now()])
                ->select(DB::raw('DATE(created_at) as date'), DB::raw('count(*) as count'))
                ->groupBy('date')
                ->orderBy('date')
                ->get();

            // Données pour le graphique des tonnages par produit
            $tonnageByProduct = DB::table('cement_orders')
                ->join('cement_order_details', 'cement_orders.id', '=', 'cement_order_details.cement_order_id')
                ->join('products', 'products.id', '=', 'cement_orders.product_id')
                ->where('cement_orders.created_by', auth()->id())
                ->select('products.name', DB::raw('SUM(cement_order_details.trips_count * cement_order_details.tonnage_per_trip) as total'))
                ->groupBy('products.id', 'products.name')
                ->orderBy('total', 'desc')
                ->limit(5)
                ->get();

            return response()->json([
                'success' => true,
                'stats' => $stats,
                'dailyOrders' => $dailyOrders,
                'tonnageByProduct' => $tonnageByProduct
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors du chargement des statistiques.',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
