<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class CustomerController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:accountant']);
    }
    
    /**
     * Affiche la liste des clients
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $customers = User::role('customer')->latest()->paginate(10);
        return view('accountant.customers.index', compact('customers'));
    }
    
    /**
     * Affiche le formulaire de création d'un client
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('accountant.customers.create');
    }
    
    /**
     * Affiche les détails d'un client
     *
     * @param \App\Models\User $customer
     * @return \Illuminate\View\View
     */
    public function show(User $customer)
    {
        return view('accountant.customers.show', compact('customer'));
    }
    
    /**
     * Affiche le formulaire d'édition d'un client
     *
     * @param \App\Models\User $customer
     * @return \Illuminate\View\View
     */
    public function edit(User $customer)
    {
        return view('accountant.customers.edit', compact('customer'));
    }
    
    /**
     * Met à jour un client
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User $customer
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, User $customer)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $customer->id,
            'phone' => 'required|string|max:255',
        ]);
        
        $customer->update($validated);
        
        return redirect()->route('accountant.customers.index')
            ->with('success', 'Client modifié avec succès');
    }
    
    /**
     * Supprime un client
     *
     * @param \App\Models\User $customer
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(User $customer)
    {
        $customer->delete();
        
        return redirect()->route('accountant.customers.index')
            ->with('success', 'Client supprimé avec succès');
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users',
                'phone' => 'required|string|max:255',
            ]);

            $user = User::create([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'phone' => $validated['phone'],
                'password' => Hash::make('password123'),
                'is_active' => true,
            ]);

            $user->assignRole('customer');

            return response()->json([
                'success' => true,
                'user' => $user,
                'message' => 'Client ajouté avec succès'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'errors' => $e->errors(),
                'message' => 'Erreur de validation'
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors de l\'ajout du client'
            ], 500);
        }
    }
}
