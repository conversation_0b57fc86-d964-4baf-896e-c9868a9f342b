<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('trip_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cement_order_id');
            $table->foreignId('cement_order_detail_id');
            $table->foreignId('trip_id')->nullable();
            $table->foreignId('truck_id');
            $table->foreignId('driver_id');
            $table->integer('trip_number');
            $table->decimal('tonnage', 8, 2)->nullable();
            $table->string('status')->default('pending');
            $table->timestamp('assigned_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->datetime('start_date');
            $table->datetime('end_date');
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('trip_assignments');
    }
};
