<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use App\Models\Product;
use Carbon\Carbon;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Récupérer les utilisateurs avec le rôle customer
        $customers = User::role('customer')->get();
        
        // Si pas de customers, créer quelques utilisateurs clients
        if ($customers->isEmpty()) {
            for ($i = 1; $i <= 5; $i++) {
                $customer = User::create([
                    'name' => "Client Test $i",
                    'email' => "client$<EMAIL>",
                    'phone' => "0123456789$i",
                    'address' => "Adresse du client $i",
                    'password' => bcrypt('password'),
                    'is_active' => true,
                ]);
                $customer->assignRole('customer');
                $customers->push($customer);
            }
        }

        // Récupérer quelques produits
        $products = Product::take(10)->get();
        
        if ($products->isEmpty()) {
            // Créer quelques produits de test si aucun n'existe
            $products = collect([
                Product::create(['name' => 'Ciment Portland', 'price' => 5000, 'unit' => 'sac']),
                Product::create(['name' => 'Fer à béton 8mm', 'price' => 800, 'unit' => 'kg']),
                Product::create(['name' => 'Fer à béton 10mm', 'price' => 850, 'unit' => 'kg']),
                Product::create(['name' => 'Fer à béton 12mm', 'price' => 900, 'unit' => 'kg']),
                Product::create(['name' => 'Gravier', 'price' => 15000, 'unit' => 'm3']),
            ]);
        }

        // Créer des commandes pour différentes périodes
        $statuses = ['pending', 'completed', 'cancelled'];
        $paymentStatuses = ['pending', 'paid'];
        $paymentMethods = ['cash', 'bank_transfer', 'check'];

        // Commandes de ce mois
        for ($i = 0; $i < 20; $i++) {
            $customer = $customers->random();
            $status = $statuses[array_rand($statuses)];
            $paymentStatus = $status === 'completed' ? 'paid' : $paymentStatuses[array_rand($paymentStatuses)];
            
            $order = Order::create([
                'reference' => 'ORD-' . date('Ym') . '-' . str_pad($i + 1, 4, '0', STR_PAD_LEFT),
                'user_id' => $customer->id,
                'total_amount' => rand(50000, 500000),
                'status' => $status,
                'payment_status' => $paymentStatus,
                'payment_method' => $paymentMethods[array_rand($paymentMethods)],
                'notes' => "Commande de test #" . ($i + 1),
                'created_at' => Carbon::now()->subDays(rand(0, 30)),
            ]);

            // Ajouter des items à la commande
            $numItems = rand(1, 5);
            $orderTotal = 0;
            
            for ($j = 0; $j < $numItems; $j++) {
                $product = $products->random();
                $quantity = rand(1, 10);
                $price = $product->price ?? rand(1000, 10000);
                $total = $quantity * $price;
                $orderTotal += $total;

                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'unit_price' => $price,
                    'total_price' => $total,
                ]);
            }

            // Mettre à jour le total de la commande
            $order->update(['total_amount' => $orderTotal]);
        }

        // Commandes du mois dernier
        for ($i = 0; $i < 15; $i++) {
            $customer = $customers->random();
            $status = $statuses[array_rand($statuses)];
            $paymentStatus = $status === 'completed' ? 'paid' : $paymentStatuses[array_rand($paymentStatuses)];
            
            $order = Order::create([
                'reference' => 'ORD-' . date('Ym', strtotime('-1 month')) . '-' . str_pad($i + 1, 4, '0', STR_PAD_LEFT),
                'user_id' => $customer->id,
                'total_amount' => rand(50000, 500000),
                'status' => $status,
                'payment_status' => $paymentStatus,
                'payment_method' => $paymentMethods[array_rand($paymentMethods)],
                'notes' => "Commande du mois dernier #" . ($i + 1),
                'created_at' => Carbon::now()->subMonth()->subDays(rand(0, 30)),
            ]);

            // Ajouter des items à la commande
            $numItems = rand(1, 3);
            $orderTotal = 0;
            
            for ($j = 0; $j < $numItems; $j++) {
                $product = $products->random();
                $quantity = rand(1, 8);
                $price = $product->price ?? rand(1000, 10000);
                $total = $quantity * $price;
                $orderTotal += $total;

                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'unit_price' => $price,
                    'total_price' => $total,
                ]);
            }

            // Mettre à jour le total de la commande
            $order->update(['total_amount' => $orderTotal]);
        }

        // Commandes de cette année (autres mois)
        for ($i = 0; $i < 25; $i++) {
            $customer = $customers->random();
            $status = $statuses[array_rand($statuses)];
            $paymentStatus = $status === 'completed' ? 'paid' : $paymentStatuses[array_rand($paymentStatuses)];
            
            $randomMonth = rand(1, 10); // Éviter le mois actuel et le mois dernier
            if ($randomMonth >= date('n') - 1) {
                $randomMonth += 2;
            }
            
            $order = Order::create([
                'reference' => 'ORD-' . date('Y') . str_pad($randomMonth, 2, '0', STR_PAD_LEFT) . '-' . str_pad($i + 1, 4, '0', STR_PAD_LEFT),
                'user_id' => $customer->id,
                'total_amount' => rand(50000, 500000),
                'status' => $status,
                'payment_status' => $paymentStatus,
                'payment_method' => $paymentMethods[array_rand($paymentMethods)],
                'notes' => "Commande de l'année #" . ($i + 1),
                'created_at' => Carbon::create(date('Y'), $randomMonth, rand(1, 28)),
            ]);

            // Ajouter des items à la commande
            $numItems = rand(1, 4);
            $orderTotal = 0;
            
            for ($j = 0; $j < $numItems; $j++) {
                $product = $products->random();
                $quantity = rand(1, 6);
                $price = $product->price ?? rand(1000, 10000);
                $total = $quantity * $price;
                $orderTotal += $total;

                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'unit_price' => $price,
                    'total_price' => $total,
                ]);
            }

            // Mettre à jour le total de la commande
            $order->update(['total_amount' => $orderTotal]);
        }
    }
}
