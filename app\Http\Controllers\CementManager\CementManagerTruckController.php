<?php

namespace App\Http\Controllers\CementManager;

use App\Http\Controllers\Controller;
use App\Models\Truck;
use App\Models\TruckCapacity;
use App\Models\Driver;
use App\Traits\TruckManagement;
use Illuminate\Http\Request;

class CementManagerTruckController extends Controller
{
    use TruckManagement;

    public function index()
    {
        $trucks = Truck::with(['capacity', 'driver'])
            ->orderBy('registration_number')
            ->paginate(10);

        return view('cement-manager.trucks.index', compact('trucks'));
    }

    public function create()
    {
        $capacities = TruckCapacity::orderBy('capacity')->get();
        $drivers = Driver::orderBy('first_name')->get();
        return view('cement-manager.trucks.create', compact('capacities', 'drivers'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'registration_number' => 'required|string|unique:trucks',
            'brand' => 'required|string',
            'model' => 'required|string',
            'truck_capacity_id' => 'required|exists:truck_capacities,id',
            'driver_id' => 'required|exists:drivers,id',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'status' => 'required|in:available,maintenance,busy',
            'notes' => 'nullable|string'
        ]);

        $truck = Truck::create($validated);

        return redirect()
            ->route('cement-manager.trucks.index')
            ->with('success', 'Véhicule ajouté avec succès');
    }

    public function edit(Truck $truck)
    {
        $capacities = TruckCapacity::orderBy('capacity')->get();
        $drivers = Driver::orderBy('first_name')->get();
        return view('cement-manager.trucks.edit', compact('truck', 'capacities', 'drivers'));
    }

    public function update(Request $request, Truck $truck)
    {
        $validated = $request->validate([
            'registration_number' => 'required|string|unique:trucks,registration_number,' . $truck->id,
            'brand' => 'required|string',
            'model' => 'required|string',
            'truck_capacity_id' => 'required|exists:truck_capacities,id',
            'driver_id' => 'required|exists:drivers,id',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'status' => 'required|in:available,maintenance,busy',
            'notes' => 'nullable|string'
        ]);

        $truck->update($validated);

        return redirect()
            ->route('cement-manager.trucks.index')
            ->with('success', 'Véhicule mis à jour avec succès');
    }

    public function destroy(Truck $truck)
    {
        $truck->delete();

        return redirect()
            ->route('cement-manager.trucks.index')
            ->with('success', 'Véhicule supprimé avec succès');
    }

    public function show(Truck $truck)
    {
        $truck->load(['capacity', 'driver']);
        return view('cement-manager.trucks.show', compact('truck'));
    }
}
