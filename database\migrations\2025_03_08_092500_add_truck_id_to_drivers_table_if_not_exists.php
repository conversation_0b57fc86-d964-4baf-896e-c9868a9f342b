<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('drivers', 'truck_id')) {
            Schema::table('drivers', function (Blueprint $table) {
                $table->unsignedBigInteger('truck_id')->nullable();
                $table->foreign('truck_id')
                      ->references('id')
                      ->on('trucks')
                      ->onDelete('set null');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('drivers', 'truck_id')) {
            Schema::table('drivers', function (Blueprint $table) {
                $table->dropForeign(['truck_id']);
                $table->dropColumn('truck_id');
            });
        }
    }
};
