<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupplyCity extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'supply_cities';

    protected $fillable = [
        'supply_id',
        'city_id',
        'vehicle_id',
        'driver_id',
        'quantity',
        'price',
        'trips',
        'remaining_quantity',
        'remaining_trips'
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'price' => 'decimal:2',
        'remaining_quantity' => 'decimal:2',
        'trips' => 'integer',
        'remaining_trips' => 'integer'
    ];

    public function supply(): BelongsTo
    {
        return $this->belongsTo(Supply::class);
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Truck::class, 'vehicle_id');
    }

    public function driver(): BelongsTo
    {
        return $this->belongsTo(Driver::class);
    }
}
