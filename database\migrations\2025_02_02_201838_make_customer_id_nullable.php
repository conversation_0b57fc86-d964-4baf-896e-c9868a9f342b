<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add the customer_id column if it doesn't exist
        if (!Schema::hasColumn('cement_order_details', 'customer_id')) {
            Schema::table('cement_order_details', function (Blueprint $table) {
                $table->foreignId('customer_id')->nullable()->after('supplier_id')->constrained('users')->onDelete('restrict');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cement_order_details', function (Blueprint $table) {
            $table->dropForeign(['customer_id']);
            $table->dropColumn('customer_id');
        });
    }
};
