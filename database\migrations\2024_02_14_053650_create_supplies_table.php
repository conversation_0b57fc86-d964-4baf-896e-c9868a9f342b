<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('supplies', function (Blueprint $table) {
            $table->id();
            $table->string('reference')->unique();
            $table->foreignId('supplier_id')->constrained();
            $table->foreignId('category_id')->constrained();
            $table->date('date');
            $table->date('expected_delivery_date');
            $table->text('notes')->nullable();
            $table->decimal('total_tonnage', 10, 2)->default(0);
            $table->decimal('total_amount', 12, 2)->default(0);
            $table->decimal('total_remaining', 12, 2)->default(0);
            $table->enum('status', ['pending', 'partial', 'completed', 'cancelled'])->default('pending');
            $table->string('rejection_reason')->nullable();
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('validator_id')->nullable()->constrained('users');
            $table->timestamp('validated_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Index pour améliorer les performances
            $table->index(['reference', 'status']);
            $table->index(['supplier_id', 'category_id']);
            $table->index(['date', 'expected_delivery_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('supplies');
    }
};
