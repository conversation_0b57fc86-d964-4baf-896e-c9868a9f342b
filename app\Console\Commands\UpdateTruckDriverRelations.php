<?php

namespace App\Console\Commands;

use App\Models\Driver;
use App\Models\Truck;
use Illuminate\Console\Command;

class UpdateTruckDriverRelations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'trucks:update-driver-relations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update truck-driver relations based on existing data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Mise à jour des relations camions-chauffeurs...');

        // Trouver tous les chauffeurs avec un truck_id
        $drivers = Driver::whereNotNull('truck_id')->get();

        foreach ($drivers as $driver) {
            // Mettre à jour le camion correspondant
            Truck::where('id', $driver->truck_id)
                ->update(['driver_id' => $driver->id]);
            
            $this->info("Chauffeur {$driver->first_name} {$driver->last_name} associé au camion #{$driver->truck_id}");
        }

        $this->info('Mise à jour terminée !');
    }
}
