<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // S'assurer que les rôles existent
        $roles = ['admin', 'accountant', 'cashier', 'customer'];
        foreach ($roles as $role) {
            Role::firstOrCreate(['name' => $role]);
        }

        $users = [
            [
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'is_active' => true,
                'role' => 'admin'
            ],
            [
                'name' => 'Comptable',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'is_active' => true,
                'role' => 'accountant'
            ],
            [
                'name' => 'Caissier',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'is_active' => true,
                'role' => 'cashier'
            ],
            [
                'name' => 'Client',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'is_active' => true,
                'role' => 'customer'
            ]
        ];

        foreach ($users as $userData) {
            $role = $userData['role'];
            unset($userData['role']);
            
            // Créer l'utilisateur s'il n'existe pas
            $user = User::firstOrCreate(
                ['email' => $userData['email']],
                $userData
            );
            
            // Assigner le rôle à l'utilisateur s'il n'en a pas déjà un
            if (!$user->hasRole($role)) {
                $user->assignRole($role);
            }
        }
    }
}
