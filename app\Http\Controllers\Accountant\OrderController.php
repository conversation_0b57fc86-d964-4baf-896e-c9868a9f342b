<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\CementOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class OrderController extends Controller
{
    public function index()
    {
        try {
            $orders = Order::with(['user', 'items'])
                ->latest()
                ->paginate(10);

            $cementOrders = CementOrder::with(['product'])
                ->where('created_by', auth()->id())
                ->latest()
                ->get();

            return view('accountant.orders.index', compact('orders', 'cementOrders'));
        } catch (\Exception $e) {
            Log::error('Erreur dans OrderController@index: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement des commandes.');
        }
    }

    public function pending()
    {
        try {
            $orders = Order::with('user')
                ->where('status', 'pending')
                ->latest()
                ->paginate(10);

            return view('accountant.orders.pending', compact('orders'));
        } catch (\Exception $e) {
            Log::error('Erreur dans OrderController@pending: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement des commandes en attente.');
        }
    }

    public function completed()
    {
        try {
            $orders = Order::with('user')
                ->where('status', 'completed')
                ->latest()
                ->paginate(10);

            return view('accountant.orders.completed', compact('orders'));
        } catch (\Exception $e) {
            Log::error('Erreur dans OrderController@completed: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement des commandes terminées.');
        }
    }

    public function show(Order $order)
    {
        try {
            $order->load(['user', 'items.product']);
            return view('accountant.orders.show', compact('order'));
        } catch (\Exception $e) {
            Log::error('Erreur dans OrderController@show: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement de la commande.');
        }
    }

    public function destroy(Order $order)
    {
        try {
            $order->delete();
            return back()->with('success', 'La commande a été supprimée avec succès.');
        } catch (\Exception $e) {
            Log::error('Erreur dans OrderController@destroy: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la suppression de la commande.');
        }
    }
}
