<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CementOrderNotification extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'cement_order_id',
        'title',
        'content',
        'type',
        'status',
        'created_by',
        'read_at'
    ];

    protected $casts = [
        'read_at' => 'datetime'
    ];

    // Relations
    public function order()
    {
        return $this->belongsTo(CementOrder::class, 'cement_order_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    // Accessors
    public function getIsReadAttribute()
    {
        return !is_null($this->read_at);
    }

    public function getIsUnreadAttribute()
    {
        return is_null($this->read_at);
    }

    // Methods
    public function markAsRead()
    {
        $this->update(['read_at' => now()]);
    }

    public function markAsUnread()
    {
        $this->update(['read_at' => null]);
    }
}
