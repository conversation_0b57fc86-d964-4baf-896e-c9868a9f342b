<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('cement_order_notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cement_order_id')->constrained()->onDelete('cascade');
            $table->foreignId('cement_order_detail_id')->constrained()->onDelete('cascade');
            $table->foreignId('trip_assignment_id')->nullable();
            $table->string('title');
            $table->text('content');
            $table->string('type')->default('new_order');
            $table->string('status')->default('unread');
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('cement_order_notifications');
    }
};
