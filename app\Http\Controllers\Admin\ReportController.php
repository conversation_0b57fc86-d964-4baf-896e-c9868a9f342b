<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Sale;
use App\Models\Supply;
use App\Models\Payment;
use App\Models\Order;
use App\Models\CreditSale;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Supplier;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Exports\TurnoverExport;
use App\Exports\ProfitExport;
use App\Exports\SalesExport;
use App\Exports\SuppliesExport;
use App\Exports\PaymentsExport;

class ReportController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:admin');
    }

    /**
     * Page principale des rapports
     */
    public function index()
    {
        return view('admin.reports.index');
    }

    /**
     * Rapport du chiffre d'affaires
     */
    public function turnover(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth()->format('Y-m-d'));

        // Chiffre d'affaires des ventes
        $salesTurnover = Sale::whereBetween('created_at', [$startDate, $endDate])
            ->where('payment_status', 'paid')
            ->sum('total_amount');

        // Chiffre d'affaires des commandes
        $ordersTurnover = Order::whereBetween('created_at', [$startDate, $endDate])
            ->where('payment_status', 'paid')
            ->sum('total_amount');

        // Détails par mois
        $monthlyData = Sale::selectRaw('
                YEAR(created_at) as year,
                MONTH(created_at) as month,
                SUM(total_amount) as total,
                COUNT(*) as count
            ')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('payment_status', 'paid')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();

        // Détails par produit
        $productData = Sale::select('supplies.reference as product_name')
            ->selectRaw('SUM(sales.total_amount) as total, COUNT(sales.id) as count')
            ->join('supplies', 'sales.supply_id', '=', 'supplies.id')
            ->whereBetween('sales.created_at', [$startDate, $endDate])
            ->where('sales.payment_status', 'paid')
            ->groupBy('supplies.id', 'supplies.reference')
            ->orderBy('total', 'desc')
            ->get();

        $totalTurnover = $salesTurnover + $ordersTurnover;

        $data = [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'sales_turnover' => $salesTurnover,
            'orders_turnover' => $ordersTurnover,
            'total_turnover' => $totalTurnover,
            'monthly_data' => $monthlyData,
            'product_data' => $productData,
        ];

        if ($request->get('export') === 'excel') {
            return $this->exportTurnoverExcel($data);
        }

        if ($request->get('export') === 'pdf') {
            return $this->exportTurnoverPdf($data);
        }

        return view('admin.reports.turnover', $data);
    }

    /**
     * Rapport des bénéfices
     */
    public function profit(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth()->format('Y-m-d'));

        // Revenus (ventes payées)
        $revenue = Sale::whereBetween('created_at', [$startDate, $endDate])
            ->where('payment_status', 'paid')
            ->sum('total_amount');

        // Coûts (approvisionnements)
        $costs = Supply::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'validated')
            ->sum('total_amount');

        $profit = $revenue - $costs;
        $profitMargin = $revenue > 0 ? ($profit / $revenue) * 100 : 0;

        // Détails par mois - Simplifions la requête
        $monthlyProfit = collect();

        // Récupérons les revenus par mois
        $monthlyRevenues = Sale::selectRaw('
                YEAR(created_at) as year,
                MONTH(created_at) as month,
                SUM(total_amount) as revenue
            ')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('payment_status', 'paid')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get()
            ->keyBy(function($item) {
                return $item->year . '-' . $item->month;
            });

        // Récupérons les coûts par mois
        $monthlyCosts = Supply::selectRaw('
                YEAR(created_at) as year,
                MONTH(created_at) as month,
                SUM(total_amount) as costs
            ')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'validated')
            ->groupBy('year', 'month')
            ->get()
            ->keyBy(function($item) {
                return $item->year . '-' . $item->month;
            });

        // Combinons les données
        $allMonths = $monthlyRevenues->keys()->merge($monthlyCosts->keys())->unique();

        foreach ($allMonths as $monthKey) {
            list($year, $month) = explode('-', $monthKey);
            $revenue = $monthlyRevenues->get($monthKey)->revenue ?? 0;
            $costs = $monthlyCosts->get($monthKey)->costs ?? 0;

            $monthlyProfit->push((object)[
                'year' => $year,
                'month' => $month,
                'revenue' => $revenue,
                'costs' => $costs,
                'profit' => $revenue - $costs
            ]);
        }

        $monthlyProfit = $monthlyProfit->sortByDesc(function($item) {
            return $item->year . str_pad($item->month, 2, '0', STR_PAD_LEFT);
        });

        $data = [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'revenue' => $revenue,
            'costs' => $costs,
            'profit' => $profit,
            'profit_margin' => $profitMargin,
            'monthly_profit' => $monthlyProfit,
        ];

        if ($request->get('export') === 'excel') {
            return $this->exportProfitExcel($data);
        }

        if ($request->get('export') === 'pdf') {
            return $this->exportProfitPdf($data);
        }

        return view('admin.reports.profit', $data);
    }

    /**
     * Rapport des ventes
     */
    public function sales(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth()->format('Y-m-d'));

        $salesQuery = Sale::with(['supply', 'city', 'createdBy'])
            ->whereBetween('created_at', [$startDate, $endDate]);

        // Filtres additionnels
        if ($request->filled('payment_status')) {
            $salesQuery->where('payment_status', $request->payment_status);
        }

        if ($request->filled('delivery_status')) {
            $salesQuery->where('delivery_status', $request->delivery_status);
        }

        $sales = $salesQuery->orderBy('created_at', 'desc')->paginate(50);

        // Statistiques
        $stats = [
            'total_sales' => $salesQuery->count(),
            'total_amount' => $salesQuery->sum('total_amount'),
            'total_paid' => $salesQuery->where('payment_status', 'paid')->sum('total_amount'),
            'total_pending' => $salesQuery->where('payment_status', 'pending')->sum('total_amount'),
            'average_sale' => $salesQuery->avg('total_amount'),
        ];

        $data = [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'sales' => $sales,
            'stats' => $stats,
            'filters' => $request->only(['payment_status', 'delivery_status']),
        ];

        if ($request->get('export') === 'excel') {
            return $this->exportSalesExcel($data);
        }

        if ($request->get('export') === 'pdf') {
            return $this->exportSalesPdf($data);
        }

        return view('admin.reports.sales', $data);
    }

    /**
     * Rapport des approvisionnements
     */
    public function supplies(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth()->format('Y-m-d'));

        $suppliesQuery = Supply::with(['supplier', 'category', 'createdBy'])
            ->whereBetween('created_at', [$startDate, $endDate]);

        // Filtres additionnels
        if ($request->filled('status')) {
            $suppliesQuery->where('status', $request->status);
        }

        if ($request->filled('supplier_id')) {
            $suppliesQuery->where('supplier_id', $request->supplier_id);
        }

        $supplies = $suppliesQuery->orderBy('created_at', 'desc')->paginate(50);

        // Statistiques
        $stats = [
            'total_supplies' => $suppliesQuery->count(),
            'total_amount' => $suppliesQuery->sum('total_amount'),
            'total_tonnage' => $suppliesQuery->sum('total_tonnage'),
            'validated_amount' => $suppliesQuery->where('status', 'validated')->sum('total_amount'),
            'pending_amount' => $suppliesQuery->where('status', 'pending')->sum('total_amount'),
        ];

        $data = [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'supplies' => $supplies,
            'stats' => $stats,
            'filters' => $request->only(['status', 'supplier_id']),
        ];

        if ($request->get('export') === 'excel') {
            return $this->exportSuppliesExcel($data);
        }

        if ($request->get('export') === 'pdf') {
            return $this->exportSuppliesPdf($data);
        }

        return view('admin.reports.supplies', $data);
    }

    /**
     * Rapport des paiements
     */
    public function payments(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth()->format('Y-m-d'));

        $paymentsQuery = Payment::with(['sale', 'cashier'])
            ->whereBetween('payment_date', [$startDate, $endDate]);

        // Filtres additionnels
        if ($request->filled('payment_method')) {
            $paymentsQuery->where('payment_method', $request->payment_method);
        }

        if ($request->filled('status')) {
            $paymentsQuery->where('status', $request->status);
        }

        $payments = $paymentsQuery->orderBy('payment_date', 'desc')->paginate(50);

        // Statistiques
        $stats = [
            'total_payments' => $paymentsQuery->count(),
            'total_amount' => $paymentsQuery->where('status', 'completed')->sum('amount'),
            'cash_payments' => $paymentsQuery->where('payment_method', 'cash')->where('status', 'completed')->sum('amount'),
            'transfer_payments' => $paymentsQuery->where('payment_method', 'bank_transfer')->where('status', 'completed')->sum('amount'),
            'pending_amount' => $paymentsQuery->where('status', 'pending')->sum('amount'),
        ];

        // Répartition par méthode de paiement
        $paymentMethods = Payment::selectRaw('payment_method, SUM(amount) as total, COUNT(*) as count')
            ->whereBetween('payment_date', [$startDate, $endDate])
            ->where('status', 'completed')
            ->groupBy('payment_method')
            ->get();

        $data = [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'payments' => $payments,
            'stats' => $stats,
            'payment_methods' => $paymentMethods,
            'filters' => $request->only(['payment_method', 'status']),
        ];

        if ($request->get('export') === 'excel') {
            return $this->exportPaymentsExcel($data);
        }

        if ($request->get('export') === 'pdf') {
            return $this->exportPaymentsPdf($data);
        }

        return view('admin.reports.payments', $data);
    }

    /**
     * Rapport des stocks faibles
     */
    public function lowStock(Request $request)
    {
        $threshold = $request->get('threshold', 10);

        $lowStockProducts = Product::where('stock_quantity', '<=', $threshold)
            ->where('is_active', true)
            ->with(['category'])
            ->orderBy('stock_quantity', 'asc')
            ->get();

        $criticalStock = Product::where('stock_quantity', '<=', 5)
            ->where('is_active', true)
            ->count();

        $data = [
            'threshold' => $threshold,
            'low_stock_products' => $lowStockProducts,
            'critical_stock' => $criticalStock,
            'total_products' => Product::where('is_active', true)->count(),
        ];

        if ($request->get('export') === 'excel') {
            return $this->exportLowStockExcel($data);
        }

        if ($request->get('export') === 'pdf') {
            return $this->exportLowStockPdf($data);
        }

        return view('admin.reports.low-stock', $data);
    }

    /**
     * Rapport des clients les plus actifs
     */
    public function topCustomers(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth()->format('Y-m-d'));

        $topCustomers = Sale::selectRaw('
                customer_name,
                customer_phone,
                customer_email,
                COUNT(*) as total_orders,
                SUM(total_amount) as total_spent,
                AVG(total_amount) as average_order,
                MAX(created_at) as last_order_date
            ')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->whereNotNull('customer_name')
            ->where('customer_name', '!=', '')
            ->groupBy('customer_name', 'customer_phone', 'customer_email')
            ->orderBy('total_spent', 'desc')
            ->limit(50)
            ->get();

        $data = [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'top_customers' => $topCustomers,
            'total_customers' => Sale::whereBetween('created_at', [$startDate, $endDate])
                ->whereNotNull('customer_name')
                ->where('customer_name', '!=', '')
                ->distinct('customer_name')
                ->count(),
        ];

        if ($request->get('export') === 'excel') {
            return $this->exportTopCustomersExcel($data);
        }

        if ($request->get('export') === 'pdf') {
            return $this->exportTopCustomersPdf($data);
        }

        return view('admin.reports.top-customers', $data);
    }

    /**
     * Rapport d'évolution mensuelle
     */
    public function monthlyEvolution(Request $request)
    {
        $year = $request->get('year', Carbon::now()->year);

        $monthlyData = collect();
        for ($month = 1; $month <= 12; $month++) {
            $startOfMonth = Carbon::createFromDate($year, $month, 1)->startOfMonth();
            $endOfMonth = Carbon::createFromDate($year, $month, 1)->endOfMonth();

            $sales = Sale::whereBetween('created_at', [$startOfMonth, $endOfMonth])
                ->where('payment_status', 'paid');

            $supplies = Supply::whereBetween('created_at', [$startOfMonth, $endOfMonth])
                ->where('status', 'validated');

            $revenue = $sales->sum('total_amount');
            $costs = $supplies->sum('total_amount');
            $profit = $revenue - $costs;

            $monthlyData->push([
                'month' => $month,
                'month_name' => Carbon::createFromDate($year, $month, 1)->format('F'),
                'sales_count' => $sales->count(),
                'revenue' => $revenue,
                'costs' => $costs,
                'profit' => $profit,
                'profit_margin' => $revenue > 0 ? ($profit / $revenue) * 100 : 0,
                'supplies_count' => $supplies->count(),
            ]);
        }

        $data = [
            'year' => $year,
            'monthly_data' => $monthlyData,
            'total_revenue' => $monthlyData->sum('revenue'),
            'total_costs' => $monthlyData->sum('costs'),
            'total_profit' => $monthlyData->sum('profit'),
        ];

        if ($request->get('export') === 'excel') {
            return $this->exportMonthlyEvolutionExcel($data);
        }

        if ($request->get('export') === 'pdf') {
            return $this->exportMonthlyEvolutionPdf($data);
        }

        return view('admin.reports.monthly-evolution', $data);
    }

    /**
     * Export du chiffre d'affaires en Excel
     */
    private function exportTurnoverExcel($data)
    {
        return \Excel::download(new TurnoverExport($data), 'chiffre_affaires_' . date('Y-m-d') . '.xlsx');
    }

    /**
     * Export du chiffre d'affaires en PDF
     */
    private function exportTurnoverPdf($data)
    {
        $pdf = \PDF::loadView('admin.reports.pdf.turnover', $data);
        return $pdf->download('chiffre_affaires_' . date('Y-m-d') . '.pdf');
    }

    /**
     * Export des bénéfices en Excel
     */
    private function exportProfitExcel($data)
    {
        return \Excel::download(new ProfitExport($data), 'benefices_' . date('Y-m-d') . '.xlsx');
    }

    /**
     * Export des bénéfices en PDF
     */
    private function exportProfitPdf($data)
    {
        $pdf = \PDF::loadView('admin.reports.pdf.profit', $data);
        return $pdf->download('benefices_' . date('Y-m-d') . '.pdf');
    }

    /**
     * Export des ventes en Excel
     */
    private function exportSalesExcel($data)
    {
        return \Excel::download(new SalesExport($data), 'ventes_' . date('Y-m-d') . '.xlsx');
    }

    /**
     * Export des ventes en PDF
     */
    private function exportSalesPdf($data)
    {
        $pdf = \PDF::loadView('admin.reports.pdf.sales', $data);
        return $pdf->download('ventes_' . date('Y-m-d') . '.pdf');
    }

    /**
     * Export des approvisionnements en Excel
     */
    private function exportSuppliesExcel($data)
    {
        return \Excel::download(new SuppliesExport($data), 'approvisionnements_' . date('Y-m-d') . '.xlsx');
    }

    /**
     * Export des approvisionnements en PDF
     */
    private function exportSuppliesPdf($data)
    {
        $pdf = \PDF::loadView('admin.reports.pdf.supplies', $data);
        return $pdf->download('approvisionnements_' . date('Y-m-d') . '.pdf');
    }

    /**
     * Export des paiements en Excel
     */
    private function exportPaymentsExcel($data)
    {
        return \Excel::download(new PaymentsExport($data), 'paiements_' . date('Y-m-d') . '.xlsx');
    }

    /**
     * Export des paiements en PDF
     */
    private function exportPaymentsPdf($data)
    {
        $pdf = \PDF::loadView('admin.reports.pdf.payments', $data);
        return $pdf->download('paiements_' . date('Y-m-d') . '.pdf');
    }

    /**
     * Export des stocks faibles en Excel
     */
    private function exportLowStockExcel($data)
    {
        return \Excel::download(new \App\Exports\LowStockExport($data), 'stocks_faibles_' . date('Y-m-d') . '.xlsx');
    }

    /**
     * Export des stocks faibles en PDF
     */
    private function exportLowStockPdf($data)
    {
        $pdf = \PDF::loadView('admin.reports.pdf.low-stock', $data);
        return $pdf->download('stocks_faibles_' . date('Y-m-d') . '.pdf');
    }

    /**
     * Export des top clients en Excel
     */
    private function exportTopCustomersExcel($data)
    {
        return \Excel::download(new \App\Exports\TopCustomersExport($data), 'top_clients_' . date('Y-m-d') . '.xlsx');
    }

    /**
     * Export des top clients en PDF
     */
    private function exportTopCustomersPdf($data)
    {
        $pdf = \PDF::loadView('admin.reports.pdf.top-customers', $data);
        return $pdf->download('top_clients_' . date('Y-m-d') . '.pdf');
    }

    /**
     * Export de l'évolution mensuelle en Excel
     */
    private function exportMonthlyEvolutionExcel($data)
    {
        return \Excel::download(new \App\Exports\MonthlyEvolutionExport($data), 'evolution_mensuelle_' . $data['year'] . '.xlsx');
    }

    /**
     * Export de l'évolution mensuelle en PDF
     */
    private function exportMonthlyEvolutionPdf($data)
    {
        $pdf = \PDF::loadView('admin.reports.pdf.monthly-evolution', $data);
        return $pdf->download('evolution_mensuelle_' . $data['year'] . '.pdf');
    }
}
