<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        // Supprimer d'abord toutes les tables dépendantes
        Schema::disableForeignKeyConstraints();
        
        // Supprimer les anciennes tables si elles existent
        Schema::dropIfExists('supply_items');
        Schema::dropIfExists('supply_cities');
        Schema::dropIfExists('supply_details');
        Schema::dropIfExists('supplies_details');
        Schema::dropIfExists('supplies_notifications');
        
        // Supprimer la table principale
        Schema::dropIfExists('supplies');
        
        // Créer la nouvelle table supplies
        Schema::create('supplies', function (Blueprint $table) {
            $table->id();
            $table->string('reference')->unique();
            $table->foreignId('supplier_id')->constrained();
            $table->foreignId('category_id')->constrained();
            $table->date('date');
            $table->date('expected_delivery_date');
            $table->text('notes')->nullable();
            $table->decimal('total_tonnage', 10, 2);
            $table->decimal('total_amount', 10, 2);
            $table->decimal('total_remaining', 10, 2);
            $table->string('status');
            $table->text('rejection_reason')->nullable();
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('validator_id')->nullable()->constrained('users');
            $table->timestamp('validated_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::enableForeignKeyConstraints();
    }

    public function down()
    {
        Schema::dropIfExists('supplies');
    }
};
