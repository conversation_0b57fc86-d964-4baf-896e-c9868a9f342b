<?php

namespace App\Http\Controllers\CementManager;

use App\Http\Controllers\Controller;
use App\Models\Truck;
use App\Models\TruckCapacity;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class TruckController extends Controller
{
    public function create()
    {
        $capacities = TruckCapacity::all();
        return view('cement-manager.trucks.create', compact('capacities'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'registration_number' => 'required|string|max:255|unique:trucks',
            'truck_capacity_id' => 'required|exists:truck_capacities,id',
            'brand' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'status' => 'required|in:available,maintenance,busy',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        DB::beginTransaction();
        try {
            $truck = Truck::create($request->all());

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Véhicule ajouté avec succès',
                'truck' => $truck
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de l\'ajout du véhicule: ' . $e->getMessage());
            
            return response()->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de l\'ajout du véhicule: ' . $e->getMessage()
            ], 500);
        }
    }

    public function index()
    {
        $trucks = Truck::with('capacity')->get();
        return view('cement-manager.trucks.index', compact('trucks'));
    }

    public function show($id)
    {
        $truck = Truck::with('capacity')->findOrFail($id);
        return view('cement-manager.trucks.show', compact('truck'));
    }
}
