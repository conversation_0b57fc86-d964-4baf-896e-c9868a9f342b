<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Supprimer la table existante pour la recréer avec la structure complète
        Schema::dropIfExists('supply_cities');

        // Créer la table avec toutes les colonnes nécessaires
        Schema::create('supply_cities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('supply_id')->constrained()->onDelete('cascade');
            $table->foreignId('city_id')->constrained();
            $table->foreignId('vehicle_id')->constrained('trucks');
            $table->foreignId('driver_id')->nullable()->constrained('drivers');
            $table->decimal('quantity', 15, 2)->comment('Quantité totale en tonnes');
            $table->decimal('remaining_quantity', 15, 2)->nullable()->comment('Quantité restante en tonnes');
            $table->decimal('price', 15, 2)->comment('Prix unitaire');
            $table->integer('trips')->default(1)->comment('Nombre total de voyages');
            $table->integer('remaining_trips')->nullable()->comment('Nombre de voyages restants');
            $table->timestamps();
            $table->softDeletes();

            // Index pour améliorer les performances
            $table->index(['supply_id', 'city_id']);
            $table->index(['vehicle_id']);
            $table->index(['driver_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('supply_cities');
    }
};
