<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Trip extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'cement_order_detail_id',
        'truck_id',
        'driver_id',
        'trip_number',
        'status',
        'started_at',
        'completed_at',
        'notes'
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    public function cementOrderDetail(): BelongsTo
    {
        return $this->belongsTo(CementOrderDetail::class);
    }

    public function truck(): BelongsTo
    {
        return $this->belongsTo(Truck::class);
    }

    public function driver(): BelongsTo
    {
        return $this->belongsTo(Driver::class);
    }

    public function tripAssignments(): HasMany
    {
        return $this->hasMany(TripAssignment::class);
    }

    // Accessors
    public function getReferenceAttribute()
    {
        return 'V' . str_pad($this->trip_number, 3, '0', STR_PAD_LEFT);
    }
}
