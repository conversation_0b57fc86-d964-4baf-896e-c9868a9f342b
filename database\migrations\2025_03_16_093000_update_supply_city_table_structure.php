<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Désactiver les contraintes de clé étrangère
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Supprimer toutes les contraintes de clé étrangère existantes
        Schema::table('supply_cities', function (Blueprint $table) {
            $foreignKeys = Schema::getConnection()
                ->getDoctrineSchemaManager()
                ->listTableForeignKeys('supply_cities');

            foreach ($foreignKeys as $foreignKey) {
                $table->dropForeign($foreignKey->getName());
            }
        });

        // Mettre à jour la structure de la table
        Schema::table('supply_cities', function (Blueprint $table) {
            // Supprimer la colonne truck_id si elle existe
            if (Schema::hasColumn('supply_cities', 'truck_id')) {
                $table->dropForeign(['truck_id']);
                $table->dropColumn('truck_id');
            }

            // Ajouter la colonne vehicle_id si elle n'existe pas
            if (!Schema::hasColumn('supply_cities', 'vehicle_id')) {
                $table->unsignedBigInteger('vehicle_id')->after('trips');
            }

            // Ajouter les nouvelles contraintes de clé étrangère
            $table->foreign('supply_id')
                  ->references('id')
                  ->on('supplies')
                  ->onDelete('cascade');

            $table->foreign('city_id')
                  ->references('id')
                  ->on('cities')
                  ->onDelete('cascade');

            $table->foreign('vehicle_id')
                  ->references('id')
                  ->on('vehicles')
                  ->onDelete('cascade');
        });

        // Réactiver les contraintes de clé étrangère
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('supply_cities', function (Blueprint $table) {
            $table->dropForeign(['supply_id']);
            $table->dropForeign(['city_id']);
            $table->dropForeign(['vehicle_id']);
            $table->dropColumn('vehicle_id');
            $table->unsignedBigInteger('truck_id')->nullable();
        });
    }
};
