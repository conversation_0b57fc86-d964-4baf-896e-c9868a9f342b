<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\Category;

class CheckCategories extends Command
{
    protected $signature = 'check:categories';
    protected $description = 'Vérifie l\'état des catégories dans la base de données';

    public function handle()
    {
        $this->info('Vérification des catégories...');

        // 1. Vérifier la connexion à la base de données
        $connection = DB::connection();
        $this->info('Base de données: ' . $connection->getDatabaseName());

        // 2. Vérifier si la table existe
        $tables = $connection->getDoctrineSchemaManager()->listTableNames();
        $this->info('Tables disponibles: ' . implode(', ', $tables));

        if (!in_array('categories', $tables)) {
            $this->error('La table categories n\'existe pas!');
            return;
        }

        // 3. Vérifier la structure de la table
        $columns = $connection->getDoctrineSchemaManager()->listTableColumns('categories');
        $this->info('Colonnes de la table categories: ' . implode(', ', array_keys((array)$columns)));

        // 4. Compter les catégories
        $count = Category::count();
        $this->info('Nombre total de catégories: ' . $count);

        // 5. Afficher toutes les catégories
        $categories = Category::all();
        $this->info('Liste des catégories:');
        foreach ($categories as $category) {
            $this->line("- {$category->name} (ID: {$category->id}, Type: {$category->type}, Actif: {$category->is_active})");
        }
    }
}
