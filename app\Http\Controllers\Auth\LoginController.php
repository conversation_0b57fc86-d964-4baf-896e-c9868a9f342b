<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LoginController extends Controller
{
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    public function showLoginForm()
    {
        return view('auth.login');
    }

    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
        ]);

        if (Auth::attempt($credentials)) {
            $request->session()->regenerate();

            $user = Auth::user();
            
            if (!$user->is_active) {
                Auth::logout();
                return back()->withErrors([
                    'email' => 'Votre compte est désactivé.',
                ]);
            }

            if ($user->hasRole('admin')) {
                return redirect()->route('admin.dashboard');
            }
            
            if ($user->hasRole('accountant')) {
                return redirect()->route('accountant.dashboard');
            }
            
            if ($user->hasRole('cement_manager')) {
                return redirect()->route('cement-manager.dashboard');
            }

            if ($user->hasRole('cashier')) {
                return redirect()->route('cashier.dashboard');
            }

            if ($user->hasRole('iron_manager')) {
                return redirect()->route('iron-manager.dashboard');
            }

            if ($user->hasRole('customer')) {
                return redirect()->route('customer.dashboard');
            }

            if ($user->hasRole('customer_service')) {
                return redirect()->route('customer-service.dashboard');
            }

            // Si aucun rôle spécifique n'est trouvé
            return redirect()->route('login')->with('error', 'Vous n\'avez pas les permissions nécessaires.');
        }

        return back()->withErrors([
            'email' => 'Les identifiants fournis ne correspondent pas à nos enregistrements.',
        ])->onlyInput('email');
    }

    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect('/login');
    }
}
