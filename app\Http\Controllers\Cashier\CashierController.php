<?php

namespace App\Http\Controllers\Cashier;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Sale;
use App\Models\Payment;
use App\Models\PaymentSchedule;
use App\Models\CementOrder;
use App\Models\CreditSale;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CashierController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:cashier');
    }

    public function dashboard()
    {
        // Récupérer les statistiques pour le tableau de bord
        $totalSales = Sale::whereIn('status', ['pending_payment', 'partially_paid', 'paid'])
            ->count();
        
        $pendingPayments = Sale::where('status', 'pending_payment')->count();
        $partiallyPaid = Sale::where('status', 'partially_paid')->count();
        $completedPayments = Sale::where('status', 'paid')->count();
        
        $totalAmount = Sale::whereIn('status', ['pending_payment', 'partially_paid', 'paid'])
            ->sum('total_amount');
        
        $paidAmount = Payment::sum('amount');
        $remainingAmount = $totalAmount - $paidAmount;
        
        // Récupérer les paiements récents
        $latest_payments = Payment::with(['sale', 'sale.supply'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();
            
        // Récupérer les ventes récentes
        $recent_sales = Sale::orderBy('created_at', 'desc')
            ->take(5)
            ->get();
            
        // Récupérer les dernières ventes à crédit
        $latest_credit_sales = CreditSale::with(['detail.customer', 'detail.product'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();
            
        // Récupérer les produits disponibles
        $available_products = Product::with('category')
            ->where('stock_quantity', '>', 0)
            ->orderBy('category_id')
            ->orderBy('name')
            ->take(10)
            ->get();
            
        // Statistiques journalières, hebdomadaires et mensuelles
        $today = now()->startOfDay();
        $weekStart = now()->startOfWeek();
        $monthStart = now()->startOfMonth();
        
        // Récupérer les statistiques des commandes de ciment
        $pendingCementOrders = CementOrder::where('status', 'pending')->count();
        $processingCementOrders = CementOrder::where('status', 'processing')->count();
        $completedCementOrders = CementOrder::where('status', 'completed')->count();
        $cancelledCementOrders = CementOrder::where('status', 'cancelled')->count();
        
        // Récupérer les statistiques des ventes à crédit
        $unpaidCreditSales = CreditSale::where('status', 'pending')->count();
        $partiallyPaidCreditSales = CreditSale::where('status', 'partial')->count();
        $paidCreditSales = CreditSale::where('status', 'completed')->count();
        
        $stats = [
            // Statistiques journalières
            'daily_sales_count' => Sale::whereDate('created_at', $today)->count(),
            'daily_sales_amount' => Sale::whereDate('created_at', $today)->sum('total_amount'),
            'daily_payments_count' => Payment::whereDate('created_at', $today)->count(),
            'daily_payments_amount' => Payment::whereDate('created_at', $today)->sum('amount'),
            
            // Statistiques hebdomadaires
            'weekly_sales_count' => Sale::where('created_at', '>=', $weekStart)->count(),
            'weekly_sales_amount' => Sale::where('created_at', '>=', $weekStart)->sum('total_amount'),
            'weekly_payments_count' => Payment::where('created_at', '>=', $weekStart)->count(),
            'weekly_payments_amount' => Payment::where('created_at', '>=', $weekStart)->sum('amount'),
            
            // Statistiques mensuelles
            'monthly_sales_count' => Sale::where('created_at', '>=', $monthStart)->count(),
            'monthly_sales_amount' => Sale::where('created_at', '>=', $monthStart)->sum('total_amount'),
            'monthly_payments_count' => Payment::where('created_at', '>=', $monthStart)->count(),
            'monthly_payments_amount' => Payment::where('created_at', '>=', $monthStart)->sum('amount'),
            
            // Statistiques des commandes de ciment
            'pending_cement_orders' => $pendingCementOrders,
            'processing_cement_orders' => $processingCementOrders,
            'completed_cement_orders' => $completedCementOrders,
            'cancelled_cement_orders' => $cancelledCementOrders,
            'total_cement_orders' => $pendingCementOrders + $processingCementOrders + $completedCementOrders + $cancelledCementOrders,
            
            // Statistiques des ventes à crédit
            'unpaid_credit_sales' => $unpaidCreditSales,
            'partially_paid_credit_sales' => $partiallyPaidCreditSales,
            'paid_credit_sales' => $paidCreditSales,
            'total_credit_sales' => $unpaidCreditSales + $partiallyPaidCreditSales + $paidCreditSales,
        ];
        
        return view('cashier.dashboard', compact(
            'totalSales', 
            'pendingPayments', 
            'partiallyPaid', 
            'completedPayments',
            'totalAmount',
            'paidAmount',
            'remainingAmount',
            'latest_payments',
            'latest_credit_sales',
            'available_products',
            'recent_sales',
            'stats'
        ));
    }
}
