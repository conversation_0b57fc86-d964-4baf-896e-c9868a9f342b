<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolesAndPermissionsSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Création des rôles s'ils n'existent pas
        $roles = [
            'admin',
            'accountant',
            'cement_manager',
            'iron_manager',
            'cashier',
            'customer_service',
            'customer',
            'driver'  // Ajout du rôle chauffeur
        ];

        foreach ($roles as $role) {
            Role::firstOrCreate(['name' => $role]);
        }

        // Créer un utilisateur admin si non existant
        $admin = User::where('email', '<EMAIL>')->first();
        
        if (!$admin) {
            $admin = User::create([
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'is_active' => true,
            ]);
        }

        // Assigner le rôle admin
        if (!$admin->hasRole('admin')) {
            $admin->assignRole('admin');
        }
    }
}
