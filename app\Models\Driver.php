<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Validation\ValidationException;

class Driver extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'license_number',
        'license_expiry',
        'address',
        'notes',
        'status',
        'is_active',
        'truck_id'
    ];

    protected $casts = [
        'license_expiry' => 'date',
        'is_active' => 'boolean'
    ];

    protected $appends = ['full_name'];

    /**
     * Get the truck associated with the driver.
     */
    public function truck()
    {
        return $this->belongsTo(Truck::class);
    }

    /**
     * Get the driver's deliveries.
     */
    public function deliveries()
    {
        return $this->hasMany(Delivery::class);
    }

    /**
     * Get the driver's trips.
     */
    public function trips()
    {
        return $this->hasMany(Trip::class);
    }

    /**
     * Get the driver's trip assignments.
     */
    public function tripAssignments()
    {
        return $this->hasMany(TripAssignment::class);
    }

    /**
     * Get the user associated with the driver (if exists).
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the driver's full name.
     */
    public function getFullNameAttribute()
    {
        $firstName = trim($this->first_name ?? '');
        $lastName = trim($this->last_name ?? '');
        
        if (empty($firstName) && empty($lastName)) {
            return 'Non assigné';
        }
        
        return trim($firstName . ' ' . $lastName);
    }
}
