<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\CementOrder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;

class CementOrderStatsController extends Controller
{
    public function index(Request $request)
    {
        try {
            // Période pour le graphique (par défaut 7 jours)
            $days = $request->get('period', 7);
            $startDate = Carbon::now()->subDays($days - 1)->startOfDay();
            $endDate = Carbon::now()->endOfDay();

            // Statistiques des bons de commande
            $stats = [
                'total_cement_orders' => CementOrder::where('created_by', auth()->id())
                    ->count(),
                'pending_cement_orders' => CementOrder::where('created_by', auth()->id())
                    ->where('status', 'pending')
                    ->count(),
                'total_cement_tonnage' => CementOrder::where('created_by', auth()->id())
                    ->sum('total_tonnage') ?? 0,
                'monthly_cement_orders' => CementOrder::where('created_by', auth()->id())
                    ->whereMonth('created_at', Carbon::now()->month)
                    ->count()
            ];

            // Derniers bons de commande
            $latestCementOrders = CementOrder::with(['product', 'details.destination'])
                ->where('created_by', auth()->id())
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();

            // Données pour le graphique des commandes par jour
            $dailyOrders = CementOrder::where('created_by', auth()->id())
                ->whereBetween('created_at', [$startDate, $endDate])
                ->select(DB::raw('DATE(created_at) as date'), DB::raw('count(*) as count'))
                ->groupBy('date')
                ->orderBy('date')
                ->get();

            // Créer un tableau avec toutes les dates de la période
            $dateRange = collect();
            for ($date = $startDate->copy(); $date <= $endDate; $date->addDay()) {
                $dateRange->push($date->format('Y-m-d'));
            }

            // Fusionner avec les données réelles et remplir les jours manquants avec 0
            $chartData = $dateRange->map(function ($date) use ($dailyOrders) {
                $orderCount = $dailyOrders->firstWhere('date', $date);
                return [
                    'date' => $date,
                    'count' => $orderCount ? $orderCount->count : 0
                ];
            });

            // Formater les données pour le graphique
            $formattedChartData = [
                'labels' => $chartData->map(function ($item) {
                    return Carbon::parse($item['date'])->format('d/m');
                }),
                'data' => $chartData->pluck('count')
            ];

            return response()->json([
                'success' => true,
                'stats' => $stats,
                'latestOrders' => $latestCementOrders,
                'chartData' => $formattedChartData
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors du chargement des statistiques.',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
