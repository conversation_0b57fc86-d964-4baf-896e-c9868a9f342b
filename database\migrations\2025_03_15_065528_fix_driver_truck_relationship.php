<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Supprimer la contrainte de clé étrangère et la colonne driver_id de la table trucks
        Schema::table('trucks', function (Blueprint $table) {
            $table->dropForeign(['driver_id']);
            $table->dropColumn('driver_id');
        });

        // S'assurer que la colonne truck_id existe dans la table drivers avec la bonne contrainte
        if (!Schema::hasColumn('drivers', 'truck_id')) {
            Schema::table('drivers', function (Blueprint $table) {
                $table->unsignedBigInteger('truck_id')->nullable()->after('status');
                $table->foreign('truck_id')->references('id')->on('trucks')->onDelete('set null');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restaurer la colonne driver_id dans la table trucks
        Schema::table('trucks', function (Blueprint $table) {
            $table->unsignedBigInteger('driver_id')->nullable()->after('truck_capacity_id');
            $table->foreign('driver_id')->references('id')->on('drivers')->onDelete('set null');
        });

        // Supprimer la colonne truck_id de la table drivers si elle existe
        if (Schema::hasColumn('drivers', 'truck_id')) {
            Schema::table('drivers', function (Blueprint $table) {
                $table->dropForeign(['truck_id']);
                $table->dropColumn('truck_id');
            });
        }
    }
};
