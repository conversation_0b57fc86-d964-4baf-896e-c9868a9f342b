<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('supply_cities', function (Blueprint $table) {
            // Supprimer l'ancienne contrainte de clé étrangère
            $table->dropForeign(['vehicle_id']);
            
            // Ajouter la nouvelle contrainte qui pointe vers la table trucks
            $table->foreign('vehicle_id')
                  ->references('id')
                  ->on('trucks')
                  ->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::table('supply_cities', function (Blueprint $table) {
            // Supprimer la nouvelle contrainte
            $table->dropForeign(['vehicle_id']);
            
            // Remettre l'ancienne contrainte
            $table->foreign('vehicle_id')
                  ->references('id')
                  ->on('vehicles')
                  ->onDelete('cascade');
        });
    }
};
