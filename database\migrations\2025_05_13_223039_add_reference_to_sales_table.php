<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('sales', 'reference')) {
            Schema::table('sales', function (Blueprint $table) {
                $table->string('reference')->nullable()->after('id');
            });

            // Mettre à jour les enregistrements existants avec des références générées
            $sales = DB::table('sales')->get();
            foreach ($sales as $sale) {
                $reference = 'VNT-' . str_pad($sale->id, 6, '0', STR_PAD_LEFT);
                DB::table('sales')->where('id', $sale->id)->update(['reference' => $reference]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sales', function (Blueprint $table) {
            $table->dropColumn('reference');
        });
    }
};
