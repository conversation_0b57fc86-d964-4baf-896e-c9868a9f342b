<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('trips', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cement_order_id');
            $table->foreignId('cement_order_detail_id');
            $table->foreignId('truck_id');
            $table->foreignId('driver_id');
            $table->integer('trip_number');
            $table->decimal('tonnage', 8, 2)->nullable();
            $table->string('status')->default('pending');
            $table->timestamp('start_time')->nullable();
            $table->timestamp('end_time')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('trips');
    }
};
