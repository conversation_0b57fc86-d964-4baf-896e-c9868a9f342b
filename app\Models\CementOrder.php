<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;
use App\Constants\Currency;

class CementOrder extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'reference',
        'product_id',
        'total_tonnage',
        'total_amount',
        'paid_amount',
        'remaining_amount',
        'status',
        'created_by',
        'validated_by',
        'validated_at',
        'notes'
    ];

    protected $casts = [
        'validated_at' => 'datetime',
        'total_tonnage' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'remaining_amount' => 'decimal:2'
    ];

    // Relations
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function validator()
    {
        return $this->belongsTo(User::class, 'validated_by');
    }

    public function product()
    {
        return $this->belongsTo(Product::class)->select('products.id', 'products.name', 'products.unit');
    }

    public function details()
    {
        return $this->hasMany(CementOrderDetail::class);
    }

    public function customers()
    {
        return $this->hasManyThrough(
            User::class,
            CementOrderDetail::class,
            'cement_order_id', // Clé étrangère sur cement_order_details
            'id', // Clé primaire sur users
            'id', // Clé locale sur cement_orders
            'customer_id' // Clé distante sur cement_order_details
        )->select('users.id', 'users.name', 'users.email', 'users.phone');
    }

    public function payments()
    {
        return $this->hasMany(CementOrderPayment::class);
    }

    public function notifications()
    {
        return $this->hasMany(CementOrderNotification::class);
    }

    public function assignments()
    {
        return $this->hasMany(Assignment::class);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeProcessing($query)
    {
        return $query->where('status', 'processing');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    // Accessors & Mutators
    public function getRemainingTonnageAttribute()
    {
        return $this->total_tonnage - $this->details->sum('delivered_quantity');
    }

    public function getIsCompletedAttribute()
    {
        return $this->status === 'completed';
    }

    public function getIsPendingAttribute()
    {
        return $this->status === 'pending';
    }

    public function getIsProcessingAttribute()
    {
        return $this->status === 'processing';
    }

    public function getIsCancelledAttribute()
    {
        return $this->status === 'cancelled';
    }

    // Accesseurs pour le formatage
    public function getFormattedTotalTonnageAttribute()
    {
        return number_format($this->total_tonnage, 2) . ' T';
    }

    public function getFormattedTotalAmountAttribute()
    {
        return Currency::format($this->total_amount);
    }

    public function getFormattedPaidAmountAttribute()
    {
        return Currency::format($this->total_amount - $this->remaining_amount);
    }

    public function getFormattedRemainingAmountAttribute()
    {
        return Currency::format($this->remaining_amount);
    }
}
