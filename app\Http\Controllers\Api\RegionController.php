<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Region;
use Illuminate\Http\Request;

class RegionController extends Controller
{
    /**
     * Récupère les villes d'une région
     */
    public function getCities(Region $region)
    {
        try {
            $cities = $region->cities()
                ->select('id', 'name')
                ->orderBy('name')
                ->get();

            return response()->json($cities);
        } catch (\Exception $e) {
            \Log::error('Erreur lors de la récupération des villes', [
                'region_id' => $region->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Erreur lors de la récupération des villes'
            ], 500);
        }
    }
}
