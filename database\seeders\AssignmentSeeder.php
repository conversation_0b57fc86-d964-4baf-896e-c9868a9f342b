<?php

namespace Database\Seeders;

use App\Models\Assignment;
use App\Models\Driver;
use App\Models\Truck;
use App\Models\CementOrder;
use App\Models\CementOrderDetail;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class AssignmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Récupérer les IDs des chauffeurs et camions existants
        $driverIds = Driver::pluck('id')->toArray();
        $truckIds = Truck::pluck('id')->toArray();
        
        // Récupérer une commande de ciment et ses détails
        $cementOrder = CementOrder::first();
        $cementOrderDetail = CementOrderDetail::first();

        if (empty($driverIds) || empty($truckIds) || !$cementOrder || !$cementOrderDetail) {
            return;
        }

        $assignments = [
            [
                'cement_order_id' => $cementOrder->id,
                'cement_order_detail_id' => $cementOrderDetail->id,
                'driver_id' => $driverIds[0],
                'truck_id' => $truckIds[0],
                'trip_number' => 1,
                'tonnage' => 25.5,
                'status' => 'completed',
                'start_date' => Carbon::now()->subDays(5),
                'end_date' => Carbon::now()->subDays(4),
                'notes' => 'Livraison effectuée avec succès'
            ],
            [
                'cement_order_id' => $cementOrder->id,
                'cement_order_detail_id' => $cementOrderDetail->id,
                'driver_id' => $driverIds[0],
                'truck_id' => $truckIds[1],
                'trip_number' => 2,
                'tonnage' => 30.0,
                'status' => 'in_progress',
                'start_date' => Carbon::now()->subDay(),
                'end_date' => null,
                'notes' => 'En route pour la livraison'
            ],
            [
                'cement_order_id' => $cementOrder->id,
                'cement_order_detail_id' => $cementOrderDetail->id,
                'driver_id' => $driverIds[1],
                'truck_id' => $truckIds[2],
                'trip_number' => 3,
                'tonnage' => 28.5,
                'status' => 'pending',
                'start_date' => Carbon::now()->addDays(2),
                'end_date' => null,
                'notes' => 'Planifié pour livraison'
            ]
        ];

        foreach ($assignments as $assignment) {
            Assignment::create($assignment);
        }
    }
}
