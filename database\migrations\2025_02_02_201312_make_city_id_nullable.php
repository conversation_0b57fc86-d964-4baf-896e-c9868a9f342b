<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Supprimer d'abord la contrainte de clé étrangère
        Schema::table('cement_order_details', function (Blueprint $table) {
            $table->dropForeign(['destination_id']);
        });

        // Modifier la colonne pour la rendre nullable
        DB::statement('ALTER TABLE cement_order_details MODIFY destination_id BIGINT UNSIGNED NULL');

        // Rétablir la contrainte de clé étrangère
        Schema::table('cement_order_details', function (Blueprint $table) {
            $table->foreign('destination_id')->references('id')->on('cities');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cement_order_details', function (Blueprint $table) {
            $table->dropForeign(['destination_id']);
        });

        DB::statement('ALTER TABLE cement_order_details MODIFY destination_id BIGINT UNSIGNED NOT NULL');

        Schema::table('cement_order_details', function (Blueprint $table) {
            $table->foreign('destination_id')->references('id')->on('cities');
        });
    }
};
