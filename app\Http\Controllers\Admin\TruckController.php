<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Truck;
use App\Models\TruckCapacity;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TruckController extends Controller
{
    public function index()
    {
        $trucks = Truck::with('capacity')->get();
        return view('admin.trucks.index', compact('trucks'));
    }

    public function create()
    {
        $capacities = TruckCapacity::all();
        return view('admin.trucks.create', compact('capacities'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'registration_number' => 'required|string|max:255|unique:trucks,registration_number',
            'truck_capacity_id' => 'required|exists:truck_capacities,id',
            'brand' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'status' => 'required|in:available,maintenance,busy',
        ]);

        try {
            DB::beginTransaction();
            
            Truck::create($request->all());
            
            DB::commit();
            
            return redirect()->route('admin.trucks.index')
                ->with('success', 'Véhicule créé avec succès');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de la création du véhicule: ' . $e->getMessage());
            
            return back()->withInput()
                ->with('error', 'Une erreur est survenue lors de la création du véhicule');
        }
    }

    public function edit($id)
    {
        $truck = Truck::findOrFail($id);
        $capacities = TruckCapacity::all();
        return view('admin.trucks.edit', compact('truck', 'capacities'));
    }

    public function update(Request $request, $id)
    {
        $truck = Truck::findOrFail($id);
        
        $request->validate([
            'registration_number' => 'required|string|max:255|unique:trucks,registration_number,'.$id,
            'truck_capacity_id' => 'required|exists:truck_capacities,id',
            'brand' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'status' => 'required|in:available,maintenance,busy',
        ]);

        try {
            DB::beginTransaction();
            
            $truck->update($request->all());
            
            DB::commit();
            
            return redirect()->route('admin.trucks.index')
                ->with('success', 'Véhicule mis à jour avec succès');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de la mise à jour du véhicule: ' . $e->getMessage());
            
            return back()->withInput()
                ->with('error', 'Une erreur est survenue lors de la mise à jour du véhicule');
        }
    }

    public function destroy($id)
    {
        try {
            $truck = Truck::findOrFail($id);
            $truck->delete();
            
            return response()->json([
                'status' => 'success',
                'message' => 'Véhicule supprimé avec succès'
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la suppression du véhicule: ' . $e->getMessage());
            
            return response()->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de la suppression du véhicule'
            ], 500);
        }
    }
}
