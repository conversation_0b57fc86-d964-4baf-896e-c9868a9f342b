<?php

namespace App\Http\Controllers\Cashier;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Sale;
use Illuminate\Support\Facades\DB;

class SalesRedesignController extends Controller
{
    /**
     * Affiche la vue redessinée des ventes avec tous les éléments nécessaires
     */
    public function index()
    {
        // Charger les ventes avec leurs relations
        $sales = Sale::with(['customer', 'user', 'products', 'payments'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);
        
        // Statistiques pour les cartes
        $stats = [
            'total_sales' => Sale::count(),
            'total_amount' => Sale::sum('amount'),
            'discount_sales' => Sale::where('is_discount_sale', true)->count(),
            'unique_customers' => Sale::distinct('customer_id')->count('customer_id'),
            'weekly_growth' => 5, // Valeur fictive pour l'exemple
            'monthly_growth' => 12 // Valeur fictive pour l'exemple
        ];
        
        // Déboguer pour vérifier que les données sont bien chargées
        // dd($sales, $stats);
            
        return view('cashier.sales.redesign', compact('sales', 'stats'));
    }
}
