<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class ProfileController extends Controller
{
    public function show()
    {
        return view('profile.show', ['user' => auth()->user()]);
    }

    public function edit()
    {
        return view('profile.edit', ['user' => auth()->user()]);
    }

    public function update(Request $request)
    {
        $user = auth()->user();

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'phone' => ['nullable', 'string', 'max:20'],
            'avatar' => ['nullable', 'image', 'max:1024'], // Max 1MB
        ]);

        if ($request->hasFile('avatar')) {
            // Supprimer l'ancienne image si elle existe
            if ($user->avatar) {
                Storage::disk('public')->delete($user->avatar);
            }

            // Stocker la nouvelle image
            $path = $request->file('avatar')->store('avatars', 'public');
            $validated['avatar'] = $path;
        }

        $user->update($validated);

        if ($request->ajax()) {
            return response()->json([
                'success' => 'Profil mis à jour avec succès'
            ]);
        }

        return redirect()->route('profile.show')
            ->with('success', 'Profil mis à jour avec succès');
    }

    public function editPassword()
    {
        return view('profile.password');
    }

    public function updatePassword(Request $request)
    {
        $validated = $request->validate([
            'current_password' => ['required', 'current_password'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
        ]);

        auth()->user()->update([
            'password' => Hash::make($validated['password'])
        ]);

        if ($request->ajax()) {
            return response()->json([
                'success' => 'Mot de passe mis à jour avec succès'
            ]);
        }

        return redirect()->route('profile.show')
            ->with('success', 'Mot de passe mis à jour avec succès');
    }

    public function deleteAvatar()
    {
        $user = auth()->user();
        
        if ($user->avatar) {
            Storage::disk('public')->delete($user->avatar);
            $user->update(['avatar' => null]);
        }

        if (request()->ajax()) {
            return response()->json([
                'success' => 'Photo de profil supprimée avec succès'
            ]);
        }

        return redirect()->route('profile.show')
            ->with('success', 'Photo de profil supprimée avec succès');
    }
}
