<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Sale;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SaleController extends Controller
{
    /**
     * Affiche la liste des ventes
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        // Filtres
        $paymentStatus = $request->get('payment_status', null);
        $startDate = $request->get('start_date', null);
        $endDate = $request->get('end_date', null);
        $customerId = $request->get('customer_id', null);
        
        // Construction de la requête de base
        $query = Sale::with(['customer', 'saleItems.product']);
        
        // Appliquer les filtres si fournis
        if ($paymentStatus) {
            $query->where('payment_status', $paymentStatus);
        }
        
        if ($startDate) {
            $query->whereDate('created_at', '>=', $startDate);
        }
        
        if ($endDate) {
            $query->whereDate('created_at', '<=', $endDate);
        }
        
        if ($customerId) {
            $query->where(function($q) use ($customerId) {
                $q->where('customer_id', $customerId)
                  ->orWhereHas('customer', function($customerQuery) use ($customerId) {
                      $customerQuery->where('id', $customerId);
                  });
            });
        }
        
        // Statistiques (calculées avant la pagination)
        $totalSales = $query->count();
        $totalAmount = $query->sum('total_amount');
        $salesIds = $query->pluck('id');
        $paidAmount = Payment::whereIn('sale_id', $salesIds)->sum('amount');
        $pendingAmount = $totalAmount - $paidAmount;

        // Récupération des ventes paginées
        $sales = $query->latest()->paginate(15);
        
        // Liste des clients pour le filtre
        $customers = Customer::orderBy('name')->get();

        return view('accountant.sales.index', compact(
            'sales', 'totalSales', 'totalAmount', 'paidAmount',
            'pendingAmount', 'customers', 'paymentStatus'
        ));
    }

    /**
     * Affiche le formulaire de création d'une vente
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $customers = Customer::orderBy('name')->get();
        $products = Product::orderBy('name')->get();
        
        return view('accountant.sales.create', compact('customers', 'products'));
    }

    /**
     * Enregistre une nouvelle vente dans la base de données
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'products' => 'required|array',
            'products.*.id' => 'required|exists:products,id',
            'products.*.quantity' => 'required|numeric|min:1',
            'products.*.unit_price' => 'required|numeric|min:0',
        ]);
        
        try {
            DB::beginTransaction();
            
            // Création de la vente
            $sale = new Sale();
            $sale->customer_id = $request->customer_id;
            $sale->payment_type = $request->payment_type ?? 'cash';
            $sale->payment_status = $request->payment_status ?? 'unpaid';
            $sale->notes = $request->notes;
            
            // Calcul du montant total
            $totalAmount = 0;
            foreach ($request->products as $product) {
                $totalAmount += $product['quantity'] * $product['unit_price'];
            }
            
            $sale->total_amount = $totalAmount;
            $sale->save();
            
            // Ajout des produits à la vente
            foreach ($request->products as $productData) {
                $sale->saleItems()->create([
                    'product_id' => $productData['id'],
                    'quantity' => $productData['quantity'],
                    'unit_price' => $productData['unit_price'],
                    'subtotal' => $productData['quantity'] * $productData['unit_price']
                ]);
            }
            
            // Création d'un paiement si paiement à l'avance
            if ($request->payment_amount > 0 && $request->payment_status != 'unpaid') {
                $payment = new Payment();
                $payment->sale_id = $sale->id;
                $payment->amount = $request->payment_amount;
                $payment->method = $request->payment_method ?? 'cash';
                $payment->notes = "Paiement initial pour la vente #" . $sale->id;
                $payment->save();
            }
            
            DB::commit();
            
            return redirect()->route('accountant.sales.show', $sale->id)
                ->with('success', 'Vente créée avec succès.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Erreur lors de la création de la vente: ' . $e->getMessage());
        }
    }

    /**
     * Affiche les détails d'une vente
     *
     * @param Sale $sale
     * @return \Illuminate\View\View
     */
    public function show(Sale $sale)
    {
        $sale->load(['customer', 'saleItems.product', 'payments']);
        
        $totalPaid = $sale->payments->sum('amount');
        $balanceDue = $sale->total_amount - $totalPaid;
        
        return view('accountant.sales.show', compact('sale', 'totalPaid', 'balanceDue'));
    }

    /**
     * Affiche le formulaire d'édition d'une vente
     *
     * @param Sale $sale
     * @return \Illuminate\View\View
     */
    public function edit(Sale $sale)
    {
        $sale->load(['customer', 'saleItems.product']);
        $customers = Customer::orderBy('name')->get();
        $products = Product::orderBy('name')->get();
        
        return view('accountant.sales.edit', compact('sale', 'customers', 'products'));
    }

    /**
     * Met à jour une vente existante
     *
     * @param Request $request
     * @param Sale $sale
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Sale $sale)
    {
        $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'payment_status' => 'required|in:paid,partial,unpaid',
        ]);
        
        try {
            DB::beginTransaction();
            
            $sale->customer_id = $request->customer_id;
            $sale->payment_status = $request->payment_status;
            $sale->payment_type = $request->payment_type;
            $sale->notes = $request->notes;
            $sale->save();
            
            DB::commit();
            
            return redirect()->route('accountant.sales.show', $sale->id)
                ->with('success', 'Vente mise à jour avec succès.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Erreur lors de la mise à jour de la vente: ' . $e->getMessage());
        }
    }

    /**
     * Supprime une vente
     *
     * @param Sale $sale
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Sale $sale)
    {
        try {
            // Vérifier si la vente a des paiements associés
            if ($sale->payments()->count() > 0) {
                return back()->with('error', 'Impossible de supprimer cette vente car elle possède des paiements associés.');
            }
            
            DB::beginTransaction();
            
            // Supprimer les éléments de vente
            $sale->saleItems()->delete();
            
            // Supprimer la vente
            $sale->delete();
            
            DB::commit();
            
            return redirect()->route('accountant.sales.index')
                ->with('success', 'Vente supprimée avec succès.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Erreur lors de la suppression de la vente: ' . $e->getMessage());
        }
    }
}
