<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="test-token">
    <title>Test des Exports - Tableau de Bord Comptable</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .export-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .export-btn:hover {
            background: #0056b3;
        }
        .generate-report {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .generate-report:hover {
            background: #1e7e34;
        }
        .progress-container {
            display: none;
            margin: 20px 0;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s;
        }
        .progress-text {
            margin-top: 10px;
            text-align: center;
            color: #666;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🧪 Test des Fonctionnalités d'Export et Rapports</h1>
    
    <div class="test-section">
        <h2>📊 Exports Rapides</h2>
        <p>Testez les différents types d'export disponibles :</p>
        
        <button class="export-btn" data-export="sales-excel">
            📈 Export Ventes Excel
        </button>
        
        <button class="export-btn" data-export="payments-pdf">
            💰 Export Paiements PDF
        </button>
        
        <button class="export-btn" data-export="dashboard-pdf">
            📋 Export Tableau de Bord PDF
        </button>
    </div>
    
    <div class="test-section">
        <h2>📑 Rapports Prédéfinis</h2>
        <p>Générez des rapports automatiques :</p>
        
        <button class="generate-report" data-report="weekly">
            📅 Rapport Hebdomadaire
        </button>
        
        <button class="generate-report" data-report="monthly">
            📆 Rapport Mensuel
        </button>
        
        <button class="generate-report" data-report="performance">
            📊 Analyse de Performance
        </button>
    </div>
    
    <div class="progress-container">
        <div class="progress-bar">
            <div class="progress-fill"></div>
        </div>
        <div class="progress-text">Initialisation...</div>
    </div>
    
    <div class="result" id="result"></div>
    
    <div class="test-section">
        <h2>📝 Logs de Test</h2>
        <div id="logs" style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;">
            <div>🚀 Interface de test initialisée</div>
        </div>
    </div>

    <script>
        // URLs de base (à adapter selon votre environnement)
        const BASE_URL = 'http://127.0.0.1:8000';
        
        // Fonction pour logger les messages
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logs.innerHTML += `<div>${icon} [${timestamp}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
        }
        
        // Fonction pour afficher la progression
        function showProgress(percent, text) {
            const container = document.querySelector('.progress-container');
            const fill = document.querySelector('.progress-fill');
            const textEl = document.querySelector('.progress-text');
            
            container.style.display = 'block';
            fill.style.width = percent + '%';
            textEl.textContent = text;
        }
        
        // Fonction pour masquer la progression
        function hideProgress() {
            document.querySelector('.progress-container').style.display = 'none';
        }
        
        // Fonction pour afficher les résultats
        function showResult(message, type) {
            const result = document.getElementById('result');
            result.className = `result ${type}`;
            result.textContent = message;
            result.style.display = 'block';
            
            setTimeout(() => {
                result.style.display = 'none';
            }, 5000);
        }
        
        // Test des exports
        document.querySelectorAll('.export-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const exportType = this.dataset.export;
                log(`🔄 Test d'export: ${exportType}`);
                testExport(exportType);
            });
        });
        
        // Test des rapports
        document.querySelectorAll('.generate-report').forEach(btn => {
            btn.addEventListener('click', function() {
                const reportType = this.dataset.report;
                log(`🔄 Test de rapport: ${reportType}`);
                testReport(reportType);
            });
        });
        
        // Fonction de test d'export
        function testExport(type) {
            showProgress(10, 'Démarrage du test...');
            
            const endpoints = {
                'sales-excel': '/accountant/dashboard/export/sales-excel',
                'payments-pdf': '/accountant/dashboard/export/payments-pdf',
                'dashboard-pdf': '/accountant/dashboard/export/dashboard-pdf'
            };
            
            const url = BASE_URL + endpoints[type];
            
            showProgress(30, 'Envoi de la requête...');
            
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': 'test-token'
                },
                body: JSON.stringify({
                    start_date: '2024-01-01',
                    end_date: '2024-12-31'
                })
            })
            .then(response => {
                showProgress(70, 'Traitement de la réponse...');
                return response.json();
            })
            .then(data => {
                showProgress(100, 'Terminé !');
                log(`✅ Export ${type} réussi: ${data.message || 'OK'}`, 'success');
                showResult(`Export ${type} réussi !`, 'success');
                console.log('Données reçues:', data);
                hideProgress();
            })
            .catch(error => {
                log(`❌ Erreur export ${type}: ${error.message}`, 'error');
                showResult(`Erreur lors de l'export ${type}`, 'error');
                hideProgress();
            });
        }
        
        // Fonction de test de rapport
        function testReport(type) {
            showProgress(10, 'Génération du rapport...');
            
            const url = BASE_URL + '/accountant/dashboard/reports/generate';
            
            showProgress(30, 'Envoi de la requête...');
            
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': 'test-token'
                },
                body: JSON.stringify({ type: type })
            })
            .then(response => {
                showProgress(70, 'Traitement de la réponse...');
                return response.json();
            })
            .then(data => {
                showProgress(100, 'Rapport généré !');
                log(`✅ Rapport ${type} généré: ${data.message || 'OK'}`, 'success');
                showResult(`Rapport ${type} généré avec succès !`, 'success');
                console.log('Données du rapport:', data);
                hideProgress();
            })
            .catch(error => {
                log(`❌ Erreur rapport ${type}: ${error.message}`, 'error');
                showResult(`Erreur lors de la génération du rapport ${type}`, 'error');
                hideProgress();
            });
        }
        
        log('🎯 Interface de test prête - Cliquez sur les boutons pour tester');
    </script>
</body>
</html>
