<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class CompletePermissionsSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // === PERMISSIONS COMPLÈTES BASÉES SUR L'ANALYSE DU PROJET ===
        $permissions = [
            // === GESTION DES UTILISATEURS ===
            'view_users',
            'create_users',
            'edit_users',
            'delete_users',
            'activate_users',
            'deactivate_users',

            // === GESTION DES RÔLES ET PERMISSIONS ===
            'view_roles',
            'create_roles',
            'edit_roles',
            'delete_roles',
            'assign_roles',
            'view_permissions',
            'create_permissions',
            'edit_permissions',
            'delete_permissions',

            // === GESTION DES PRODUITS ===
            'view_products',
            'create_products',
            'edit_products',
            'delete_products',
            'manage_stock',
            'view_stock_history',

            // === GESTION DES CATÉGORIES ===
            'view_categories',
            'create_categories',
            'edit_categories',
            'delete_categories',

            // === GESTION DES FOURNISSEURS ===
            'view_suppliers',
            'create_suppliers',
            'edit_suppliers',
            'delete_suppliers',

            // === GESTION DES APPROVISIONNEMENTS ===
            'view_supplies',
            'create_supplies',
            'edit_supplies',
            'delete_supplies',
            'validate_supplies',
            'reject_supplies',

            // === GESTION DES COMMANDES ===
            'view_orders',
            'create_orders',
            'edit_orders',
            'delete_orders',
            'approve_orders',
            'reject_orders',
            'cancel_orders',

            // === GESTION DES COMMANDES CIMENT ===
            'view_cement_orders',
            'create_cement_orders',
            'edit_cement_orders',
            'delete_cement_orders',
            'approve_cement_orders',
            'assign_cement_orders',

            // === GESTION DES VENTES ===
            'view_sales',
            'create_sales',
            'edit_sales',
            'delete_sales',
            'process_sales',

            // === GESTION DES VENTES À CRÉDIT ===
            'view_credit_sales',
            'create_credit_sales',
            'edit_credit_sales',
            'delete_credit_sales',
            'manage_credit_payments',

            // === GESTION DES PAIEMENTS ===
            'view_payments',
            'create_payments',
            'edit_payments',
            'delete_payments',
            'process_payments',
            'refund_payments',

            // === GESTION DES CAMIONS ===
            'view_trucks',
            'create_trucks',
            'edit_trucks',
            'delete_trucks',
            'assign_trucks',
            'maintain_trucks',

            // === GESTION DES CHAUFFEURS ===
            'view_drivers',
            'create_drivers',
            'edit_drivers',
            'delete_drivers',
            'assign_drivers',
            'manage_driver_licenses',

            // === GESTION DES ASSIGNATIONS/VOYAGES ===
            'view_assignments',
            'create_assignments',
            'edit_assignments',
            'delete_assignments',
            'complete_assignments',

            // === GESTION DES CLIENTS ===
            'view_customers',
            'create_customers',
            'edit_customers',
            'delete_customers',
            'manage_customer_credit',

            // === GESTION DES RÉGIONS ET VILLES ===
            'view_regions',
            'create_regions',
            'edit_regions',
            'delete_regions',
            'view_cities',
            'create_cities',
            'edit_cities',
            'delete_cities',

            // === GESTION DES DESTINATIONS ===
            'view_destinations',
            'create_destinations',
            'edit_destinations',
            'delete_destinations',

            // === GESTION DES FACTURES ===
            'view_invoices',
            'create_invoices',
            'edit_invoices',
            'delete_invoices',
            'send_invoices',

            // === GESTION DES TAXES ===
            'view_taxes',
            'create_taxes',
            'edit_taxes',
            'delete_taxes',

            // === GESTION DES DÉPENSES ===
            'view_expenses',
            'create_expenses',
            'edit_expenses',
            'delete_expenses',
            'approve_expenses',

            // === RAPPORTS ET STATISTIQUES ===
            'view_reports',
            'generate_reports',
            'export_reports',
            'view_dashboard',
            'view_analytics',

            // === PARAMÈTRES SYSTÈME ===
            'view_settings',
            'edit_settings',
            'manage_system_config',

            // === GESTION DES PRIX ===
            'view_prices',
            'edit_prices',
            'manage_pricing',

            // === GESTION DES NOTIFICATIONS ===
            'view_notifications',
            'send_notifications',
            'manage_notifications',

            // === SUPPORT CLIENT ===
            'view_tickets',
            'create_tickets',
            'edit_tickets',
            'resolve_tickets',

            // === AUDIT ET LOGS ===
            'view_audit_logs',
            'export_audit_logs',
        ];

        // Créer toutes les permissions
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission, 'guard_name' => 'web']);
        }

        // === ASSIGNATION DES PERMISSIONS PAR RÔLE ===
        $rolePermissions = [
            // === SUPER ADMIN - TOUTES LES PERMISSIONS ===
            'super-admin' => $permissions,

            // === ADMIN - GESTION COMPLÈTE SAUF CERTAINES RESTRICTIONS ===
            'admin' => [
                // Utilisateurs et rôles
                'view_users', 'create_users', 'edit_users', 'delete_users', 'activate_users', 'deactivate_users',
                'view_roles', 'create_roles', 'edit_roles', 'delete_roles', 'assign_roles',
                'view_permissions', 'create_permissions', 'edit_permissions', 'delete_permissions',

                // Produits et catégories
                'view_products', 'create_products', 'edit_products', 'delete_products', 'manage_stock', 'view_stock_history',
                'view_categories', 'create_categories', 'edit_categories', 'delete_categories',

                // Fournisseurs et approvisionnements
                'view_suppliers', 'create_suppliers', 'edit_suppliers', 'delete_suppliers',
                'view_supplies', 'create_supplies', 'edit_supplies', 'delete_supplies', 'validate_supplies', 'reject_supplies',

                // Commandes
                'view_orders', 'create_orders', 'edit_orders', 'delete_orders', 'approve_orders', 'reject_orders', 'cancel_orders',
                'view_cement_orders', 'create_cement_orders', 'edit_cement_orders', 'delete_cement_orders', 'approve_cement_orders', 'assign_cement_orders',

                // Ventes et paiements
                'view_sales', 'create_sales', 'edit_sales', 'delete_sales', 'process_sales',
                'view_credit_sales', 'create_credit_sales', 'edit_credit_sales', 'delete_credit_sales', 'manage_credit_payments',
                'view_payments', 'create_payments', 'edit_payments', 'delete_payments', 'process_payments', 'refund_payments',

                // Transport
                'view_trucks', 'create_trucks', 'edit_trucks', 'delete_trucks', 'assign_trucks', 'maintain_trucks',
                'view_drivers', 'create_drivers', 'edit_drivers', 'delete_drivers', 'assign_drivers', 'manage_driver_licenses',
                'view_assignments', 'create_assignments', 'edit_assignments', 'delete_assignments', 'complete_assignments',

                // Clients et géographie
                'view_customers', 'create_customers', 'edit_customers', 'delete_customers', 'manage_customer_credit',
                'view_regions', 'create_regions', 'edit_regions', 'delete_regions',
                'view_cities', 'create_cities', 'edit_cities', 'delete_cities',
                'view_destinations', 'create_destinations', 'edit_destinations', 'delete_destinations',

                // Finances
                'view_invoices', 'create_invoices', 'edit_invoices', 'delete_invoices', 'send_invoices',
                'view_taxes', 'create_taxes', 'edit_taxes', 'delete_taxes',
                'view_expenses', 'create_expenses', 'edit_expenses', 'delete_expenses', 'approve_expenses',

                // Rapports et paramètres
                'view_reports', 'generate_reports', 'export_reports', 'view_dashboard', 'view_analytics',
                'view_settings', 'edit_settings', 'manage_system_config',
                'view_prices', 'edit_prices', 'manage_pricing',
                'view_notifications', 'send_notifications', 'manage_notifications',
                'view_audit_logs', 'export_audit_logs',
            ],

            // === COMPTABLE - GESTION FINANCIÈRE ET LOGISTIQUE ===
            'accountant' => [
                // Utilisateurs (lecture seule)
                'view_users',

                // Produits et stock
                'view_products', 'edit_products', 'manage_stock', 'view_stock_history',
                'view_categories',

                // Fournisseurs et approvisionnements
                'view_suppliers', 'create_suppliers', 'edit_suppliers',
                'view_supplies', 'create_supplies', 'edit_supplies', 'validate_supplies',

                // Commandes
                'view_orders', 'create_orders', 'edit_orders', 'approve_orders',
                'view_cement_orders', 'create_cement_orders', 'edit_cement_orders', 'approve_cement_orders',

                // Ventes et paiements
                'view_sales', 'create_sales', 'edit_sales', 'process_sales',
                'view_credit_sales', 'create_credit_sales', 'edit_credit_sales', 'manage_credit_payments',
                'view_payments', 'create_payments', 'edit_payments', 'process_payments', 'refund_payments',

                // Transport
                'view_trucks', 'create_trucks', 'edit_trucks', 'assign_trucks',
                'view_drivers', 'create_drivers', 'edit_drivers', 'assign_drivers',
                'view_assignments', 'create_assignments', 'edit_assignments',

                // Clients et géographie
                'view_customers', 'create_customers', 'edit_customers', 'manage_customer_credit',
                'view_regions', 'view_cities',
                'view_destinations', 'create_destinations', 'edit_destinations',

                // Finances
                'view_invoices', 'create_invoices', 'edit_invoices', 'send_invoices',
                'view_taxes', 'edit_taxes',
                'view_expenses', 'create_expenses', 'edit_expenses',

                // Rapports
                'view_reports', 'generate_reports', 'export_reports', 'view_dashboard', 'view_analytics',
                'view_prices', 'edit_prices',
                'view_notifications',
            ],

            // === GÉRANT CIMENT - GESTION DES COMMANDES CIMENT ET TRANSPORT ===
            'cement_manager' => [
                // Produits (lecture seule)
                'view_products', 'view_stock_history',
                'view_categories',

                // Approvisionnements (lecture seule)
                'view_supplies',

                // Commandes ciment
                'view_cement_orders', 'create_cement_orders', 'edit_cement_orders', 'assign_cement_orders',

                // Ventes
                'view_sales', 'create_sales', 'edit_sales', 'process_sales',
                'view_credit_sales', 'create_credit_sales', 'edit_credit_sales',

                // Transport
                'view_trucks', 'assign_trucks',
                'view_drivers', 'assign_drivers',
                'view_assignments', 'create_assignments', 'edit_assignments', 'complete_assignments',

                // Clients
                'view_customers', 'create_customers', 'edit_customers',
                'view_regions', 'view_cities',
                'view_destinations',

                // Rapports
                'view_reports', 'generate_reports', 'view_dashboard',
                'view_prices',
                'view_notifications',
            ],

            // === GÉRANT FER - GESTION DES COMMANDES FER (À DÉVELOPPER) ===
            'iron_manager' => [
                // Produits fer
                'view_products', 'view_stock_history',
                'view_categories',

                // Commandes fer
                'view_orders', 'create_orders', 'edit_orders',

                // Ventes
                'view_sales', 'create_sales', 'edit_sales', 'process_sales',

                // Clients
                'view_customers', 'create_customers', 'edit_customers',
                'view_regions', 'view_cities',

                // Rapports
                'view_reports', 'view_dashboard',
                'view_prices',
                'view_notifications',
            ],

            // === CAISSIER - GESTION DES VENTES ET PAIEMENTS ===
            'cashier' => [
                // Produits (lecture seule)
                'view_products', 'view_stock_history',
                'view_categories',

                // Ventes et paiements
                'view_sales', 'create_sales', 'edit_sales', 'process_sales',
                'view_credit_sales', 'create_credit_sales', 'manage_credit_payments',
                'view_payments', 'create_payments', 'process_payments',

                // Clients
                'view_customers', 'create_customers', 'edit_customers',

                // Commandes (lecture seule)
                'view_orders', 'view_cement_orders',

                // Rapports limités
                'view_dashboard',
                'view_prices',
                'view_notifications',
            ],

            // === SERVICE CLIENT - SUPPORT ET TICKETS ===
            'customer_service' => [
                // Clients
                'view_customers', 'create_customers', 'edit_customers',

                // Support
                'view_tickets', 'create_tickets', 'edit_tickets', 'resolve_tickets',

                // Commandes (lecture seule)
                'view_orders', 'view_cement_orders',

                // Notifications
                'view_notifications', 'send_notifications',

                // Dashboard limité
                'view_dashboard',
            ],

            // === CLIENT - ACCÈS LIMITÉ ===
            'customer' => [
                // Ses propres commandes
                'view_orders', 'create_orders',
                'view_cement_orders', 'create_cement_orders',

                // Ses paiements
                'view_payments',

                // Support
                'view_tickets', 'create_tickets',

                // Notifications
                'view_notifications',
            ],

            // === CHAUFFEUR - GESTION DES ASSIGNATIONS ===
            'driver' => [
                // Ses assignations
                'view_assignments', 'complete_assignments',

                // Camions (lecture seule)
                'view_trucks',

                // Notifications
                'view_notifications',
            ],
        ];

        // Assigner les permissions aux rôles
        foreach ($rolePermissions as $roleName => $rolePerms) {
            $role = Role::firstOrCreate(['name' => $roleName, 'guard_name' => 'web']);

            // Nettoyer les permissions existantes
            $role->permissions()->detach();

            // Assigner les nouvelles permissions
            $role->givePermissionTo($rolePerms);

            echo "✅ Rôle '{$roleName}': " . count($rolePerms) . " permissions assignées.\n";
        }

        echo "\n🎉 MISE À JOUR COMPLÈTE DES PERMISSIONS TERMINÉE !\n";
        echo "📊 Total: " . count($permissions) . " permissions créées\n";
        echo "👥 Rôles configurés: " . count($rolePermissions) . "\n";
    }
}
