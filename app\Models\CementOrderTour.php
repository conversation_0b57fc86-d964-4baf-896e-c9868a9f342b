<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CementOrderTour extends Model
{
    use HasFactory;

    protected $fillable = [
        'cement_order_id',
        'truck_id',
        'tour_number',
        'quantity',
        'status',
        'started_at',
        'completed_at'
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'started_at' => 'datetime',
        'completed_at' => 'datetime'
    ];

    // Relations
    public function cementOrder()
    {
        return $this->belongsTo(CementOrder::class);
    }

    public function truck()
    {
        return $this->belongsTo(Truck::class);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }
}
