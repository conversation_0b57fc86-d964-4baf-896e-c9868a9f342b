<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Database\Seeders\RolesAndPermissionsSeeder;
use Database\Seeders\UserSeeder;
use Database\Seeders\RegionsAndCitiesSeeder;
use Database\Seeders\DefaultCategoriesSeeder;
use Database\Seeders\TruckCapacitySeeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            // 1. Utilisateurs et rôles
            RolesAndPermissionsSeeder::class,
            UserSeeder::class,

            // 2. Régions et villes
            RegionsAndCitiesSeeder::class,

            // 3. Catégories et produits
            DefaultCategoriesSeeder::class,

            // 4. Autres données
            TruckCapacitySeeder::class,
        ]);
    }
}
