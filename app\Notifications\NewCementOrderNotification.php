<?php

namespace App\Notifications;

use App\Models\CementOrder;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewCementOrderNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $cementOrder;

    public function __construct(CementOrder $cementOrder)
    {
        $this->cementOrder = $cementOrder;
    }

    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    public function toMail($notifiable)
    {
        $url = route('admin.cement-orders.show', $this->cementOrder->id);

        return (new MailMessage)
            ->subject('Nouveau bon de commande de ciment')
            ->line('Un nouveau bon de commande de ciment a été créé.')
            ->line('Détails de la commande:')
            ->line('- Produit: ' . $this->cementOrder->product->name)
            ->line('- Capacité du camion: ' . $this->cementOrder->truckCapacity->tonnage . ' tonnes')
            ->line('- Nombre de tours: ' . $this->cementOrder->trips_count)
            ->line('- Destination: ' . $this->cementOrder->destination)
            ->action('Voir le bon de commande', $url);
    }

    public function toArray($notifiable)
    {
        return [
            'cement_order_id' => $this->cementOrder->id,
            'message' => 'Nouveau bon de commande de ciment créé',
            'product' => $this->cementOrder->product->name,
            'tonnage' => $this->cementOrder->truckCapacity->tonnage,
            'trips_count' => $this->cementOrder->trips_count,
            'destination' => $this->cementOrder->destination,
        ];
    }
}
