<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Validation\ValidationException;

class Truck extends Model
{
    use HasFactory, SoftDeletes;

    // Constantes pour les statuts
    public const STATUS_AVAILABLE = 'available';
    public const STATUS_ASSIGNED = 'assigned';
    public const STATUS_MAINTENANCE = 'maintenance';
    public const STATUS_BUSY = 'busy';

    protected $fillable = [
        'registration_number',
        'brand',
        'model',
        'truck_capacity_id',
        'status',
        'year',
        'notes'
    ];

    protected $casts = [
        'year' => 'integer',
        'status' => 'string'
    ];

    // Relations
    public function capacity()
    {
        return $this->belongsTo(TruckCapacity::class, 'truck_capacity_id');
    }

    /**
     * Get the driver associated with the truck.
     */
    public function driver()
    {
        return $this->belongsTo(Driver::class);
    }

    public function assignments()
    {
        return $this->hasMany(Assignment::class);
    }

    public function trips()
    {
        return $this->hasMany(Trip::class);
    }

    /**
     * Scope a query to only include available trucks.
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', self::STATUS_AVAILABLE);
    }

    /**
     * Check if the truck is available for assignment.
     */
    public function isAvailable()
    {
        return $this->status === self::STATUS_AVAILABLE;
    }

    /**
     * Check if the truck is assigned to a driver.
     */
    public function isAssigned()
    {
        return $this->status === self::STATUS_ASSIGNED;
    }

    /**
     * Assign a driver to this truck.
     */
    public function assignDriver(Driver $driver)
    {
        if ($this->isAssigned()) {
            throw ValidationException::withMessages([
                'truck' => ['Ce véhicule est déjà assigné à un autre chauffeur.']
            ]);
        }

        $this->status = self::STATUS_ASSIGNED;
        $this->save();

        $driver->truck()->associate($this);
        $driver->save();

        return true;
    }

    /**
     * Remove the current driver assignment.
     */
    public function removeDriver()
    {
        if ($driver = $this->driver) {
            $driver->truck()->dissociate();
            $driver->save();
        }

        $this->status = self::STATUS_AVAILABLE;
        $this->save();

        return true;
    }
}
