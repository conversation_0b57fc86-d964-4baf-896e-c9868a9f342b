<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Driver;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class DriverController extends Controller
{
    public function index()
    {
        $drivers = Driver::orderBy('created_at', 'desc')->paginate(10);
        return view('admin.drivers.index', compact('drivers'));
    }

    public function create()
    {
        return view('admin.drivers.create');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone_number' => 'required|string|max:20|unique:drivers,phone_number',
            'license_number' => 'required|string|max:50|unique:drivers,license_number',
            'license_expiry' => 'required|date|after:today',
            'address' => 'required|string|max:500',
            'emergency_phone' => 'required|string|max:20',
            'status' => 'required|in:available,busy,off_duty,maintenance',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();
            
            $driver = Driver::create($request->all());
            
            DB::commit();
            
            return redirect()->route('admin.drivers.index')
                ->with('success', 'Chauffeur ajouté avec succès.');
                
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de la création du chauffeur : ' . $e->getMessage());
            
            return redirect()->back()
                ->with('error', 'Une erreur est survenue lors de la création du chauffeur.')
                ->withInput();
        }
    }

    public function edit(Driver $driver)
    {
        return view('admin.drivers.edit', compact('driver'));
    }

    public function update(Request $request, Driver $driver)
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone_number' => 'required|string|max:20|unique:drivers,phone_number,' . $driver->id,
            'license_number' => 'required|string|max:50|unique:drivers,license_number,' . $driver->id,
            'license_expiry' => 'required|date|after:today',
            'address' => 'required|string|max:500',
            'emergency_phone' => 'required|string|max:20',
            'status' => 'required|in:available,busy,off_duty,maintenance',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();
            
            $driver->update($request->all());
            
            DB::commit();
            
            return redirect()->route('admin.drivers.index')
                ->with('success', 'Chauffeur mis à jour avec succès.');
                
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de la mise à jour du chauffeur : ' . $e->getMessage());
            
            return redirect()->back()
                ->with('error', 'Une erreur est survenue lors de la mise à jour du chauffeur.')
                ->withInput();
        }
    }

    public function destroy(Driver $driver)
    {
        try {
            DB::beginTransaction();
            
            $driver->delete();
            
            DB::commit();
            
            return redirect()->route('admin.drivers.index')
                ->with('success', 'Chauffeur supprimé avec succès.');
                
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de la suppression du chauffeur : ' . $e->getMessage());
            
            return redirect()->back()
                ->with('error', 'Une erreur est survenue lors de la suppression du chauffeur.');
        }
    }
}
