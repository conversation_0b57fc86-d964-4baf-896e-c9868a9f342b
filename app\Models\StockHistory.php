<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StockHistory extends Model
{
    use HasFactory;

    /**
     * Les attributs qui peuvent être assignés en masse.
     *
     * @var array<string>
     */
    protected $fillable = [
        'product_id',
        'supply_id',
        'type',
        'quantity',
        'previous_stock',
        'new_stock',
        'user_id',
        'notes'
    ];

    /**
     * Les attributs qui doivent être convertis en dates.
     *
     * @var array<string>
     */
    protected $dates = [
        'created_at',
        'updated_at'
    ];

    /**
     * Obtenir le produit associé à cet historique.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Obtenir l'approvisionnement associé à cet historique.
     */
    public function supply()
    {
        return $this->belongsTo(Supply::class);
    }

    /**
     * Obtenir l'utilisateur qui a effectué l'opération.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
