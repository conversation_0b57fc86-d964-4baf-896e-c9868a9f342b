<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Sale;
use App\Models\Supply;
use App\Models\SupplyDetail;
use Illuminate\Support\Facades\DB;

class UpdateSalesProductId extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-sales-product-id';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Met à jour les product_id des ventes existantes en fonction des approvisionnements associés';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Mise à jour des product_id des ventes existantes...');
        
        // Récupérer toutes les ventes sans product_id
        $sales = Sale::whereNull('product_id')->get();
        $this->info("Nombre de ventes à mettre à jour: {$sales->count()}");
        
        $updated = 0;
        $errors = 0;
        
        DB::beginTransaction();
        
        try {
            foreach ($sales as $sale) {
                $this->info("Traitement de la vente #{$sale->id}");
                
                // Récupérer l'approvisionnement associé
                $supply = $sale->supply;
                
                if (!$supply) {
                    $this->warn("  Approvisionnement non trouvé pour la vente #{$sale->id}");
                    $errors++;
                    continue;
                }
                
                // Récupérer le premier détail de l'approvisionnement
                $supplyDetail = SupplyDetail::where('supply_id', $supply->id)->first();
                
                if (!$supplyDetail) {
                    $this->warn("  Détail d'approvisionnement non trouvé pour la vente #{$sale->id}");
                    $errors++;
                    continue;
                }
                
                // Récupérer le produit associé au détail
                $productId = $supplyDetail->product_id;
                
                if (!$productId) {
                    $this->warn("  Produit non trouvé pour la vente #{$sale->id}");
                    $errors++;
                    continue;
                }
                
                // Mettre à jour la vente avec le product_id
                $sale->product_id = $productId;
                $sale->save();
                
                $this->info("  Vente #{$sale->id} mise à jour avec le produit #{$productId}");
                $updated++;
            }
            
            DB::commit();
            $this->info("Mise à jour terminée. {$updated} ventes mises à jour, {$errors} erreurs.");
            
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("Une erreur est survenue: {$e->getMessage()}");
            return 1;
        }
        
        return 0;
    }
}
