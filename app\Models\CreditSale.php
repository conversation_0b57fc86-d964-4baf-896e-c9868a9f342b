<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CreditSale extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'cement_order_id',
        'cement_order_detail_id',
        'trip_assignment_id',
        'cashier_id',
        'amount',
        'paid_amount',
        'remaining_amount',
        'due_date',
        'status',
        'notes'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'remaining_amount' => 'decimal:2',
        'due_date' => 'date'
    ];

    protected $with = ['detail.product', 'detail.customer', 'order'];

    // Relations
    public function order(): BelongsTo
    {
        return $this->belongsTo(CementOrder::class, 'cement_order_id');
    }

    public function detail(): BelongsTo
    {
        return $this->belongsTo(CementOrderDetail::class, 'cement_order_detail_id');
    }

    public function tripAssignment(): BelongsTo
    {
        return $this->belongsTo(TripAssignment::class);
    }

    public function cashier(): BelongsTo
    {
        return $this->belongsTo(User::class, 'cashier_id');
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    // Accessors
    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount, 0, ',', ' ') . ' FCFA';
    }

    public function getFormattedPaidAmountAttribute(): string
    {
        return number_format($this->paid_amount, 0, ',', ' ') . ' FCFA';
    }

    public function getFormattedRemainingAmountAttribute(): string
    {
        return number_format($this->remaining_amount, 0, ',', ' ') . ' FCFA';
    }

    public function getFormattedStatusAttribute(): string
    {
        return match($this->status) {
            'pending' => 'En attente',
            'partially_paid' => 'Partiellement payé',
            'paid' => 'Payé',
            default => $this->status
        };
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'danger',
            'partially_paid' => 'warning',
            'paid' => 'success',
            default => 'secondary'
        };
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopePartiallyPaid($query)
    {
        return $query->where('status', 'partially_paid');
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    public function scopeUnpaid($query)
    {
        return $query->whereIn('status', ['pending', 'partially_paid']);
    }

    public function scopeOverdue($query)
    {
        return $query->whereIn('status', ['pending', 'partially_paid'])
            ->whereNotNull('due_date')
            ->where('due_date', '<', now());
    }

    // Boot
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($creditSale) {
            if ($creditSale->amount > 0) {
                $creditSale->remaining_amount = $creditSale->amount - $creditSale->paid_amount;
                $creditSale->status = $creditSale->paid_amount > 0 ? 'partially_paid' : 'pending';
            }
        });

        static::updating(function ($creditSale) {
            if ($creditSale->isDirty('paid_amount')) {
                $creditSale->remaining_amount = $creditSale->amount - $creditSale->paid_amount;
                
                if ($creditSale->remaining_amount <= 0) {
                    $creditSale->status = 'paid';
                } elseif ($creditSale->paid_amount > 0) {
                    $creditSale->status = 'partially_paid';
                } else {
                    $creditSale->status = 'pending';
                }
            }
        });
    }
}
