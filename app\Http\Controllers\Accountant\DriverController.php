<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Driver;
use App\Models\Truck;
use Illuminate\Http\Request;

class DriverController extends Controller
{
    public function index()
    {
        $drivers = Driver::with('vehicle')->get();
        $trucks = Truck::with('driver')->get();
        
        return view('accountant.drivers.index', compact('drivers', 'trucks'));
    }

    public function create()
    {
        return view('accountant.drivers.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone_number' => 'required|string|unique:drivers,phone_number',
            'license_number' => 'required|string|unique:drivers,license_number',
            'license_expiry' => 'required|date',
            'address' => 'nullable|string',
            'emergency_contact' => 'nullable|string',
            'emergency_phone' => 'nullable|string',
            'notes' => 'nullable|string'
        ]);

        $driver = Driver::create($validated);

        return redirect()
            ->route('accountant.drivers.index')
            ->with('success', 'Chauffeur ajouté avec succès');
    }

    public function edit(Driver $driver)
    {
        return view('accountant.drivers.edit', compact('driver'));
    }

    public function update(Request $request, Driver $driver)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone_number' => 'required|string|unique:drivers,phone_number,' . $driver->id,
            'license_number' => 'required|string|unique:drivers,license_number,' . $driver->id,
            'license_expiry' => 'required|date',
            'address' => 'nullable|string',
            'emergency_contact' => 'nullable|string',
            'emergency_phone' => 'nullable|string',
            'notes' => 'nullable|string'
        ]);

        $driver->update($validated);

        return redirect()
            ->route('accountant.drivers.index')
            ->with('success', 'Chauffeur mis à jour avec succès');
    }

    public function destroy(Driver $driver)
    {
        $driver->delete();

        return redirect()
            ->route('accountant.drivers.index')
            ->with('success', 'Chauffeur supprimé avec succès');
    }

    public function getAvailableDrivers()
    {
        return Driver::where('status', 'available')->get();
    }
}
