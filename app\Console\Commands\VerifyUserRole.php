<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;

class VerifyUserRole extends Command
{
    protected $signature = 'user:verify-role {email} {role}';
    protected $description = 'Verify and assign role to a user';

    public function handle()
    {
        $email = $this->argument('email');
        $roleName = $this->argument('role');

        // Trouver l'utilisateur
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("Utilisateur avec l'email {$email} non trouvé.");
            return 1;
        }

        // Vérifier si le rôle existe
        $role = Role::where('name', $roleName)->first();
        
        if (!$role) {
            $this->error("Le rôle {$roleName} n'existe pas.");
            return 1;
        }

        // Vérifier si l'utilisateur a déjà le rôle
        if ($user->hasRole($roleName)) {
            $this->info("L'utilisateur a déjà le rôle {$roleName}.");
            return 0;
        }

        // Assigner le rôle
        $user->assignRole($roleName);
        $this->info("Le rôle {$roleName} a été assigné à l'utilisateur {$email}.");
        
        return 0;
    }
}
