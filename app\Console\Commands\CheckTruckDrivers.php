<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Truck;
use App\Models\Driver;

class CheckTruckDrivers extends Command
{
    protected $signature = 'trucks:check-drivers';
    protected $description = 'Vérifie les assignations des chauffeurs aux camions';

    public function handle()
    {
        $this->info('Vérification des assignations chauffeurs-camions...');

        $trucks = Truck::with('driver')
            ->whereNull('deleted_at')
            ->where('status', 'assigned')
            ->get();

        $this->table(
            ['ID', 'Immatriculation', 'Status', 'Driver ID', 'Chauffeur'],
            $trucks->map(function ($truck) {
                return [
                    'id' => $truck->id,
                    'registration' => $truck->registration_number,
                    'status' => $truck->status,
                    'driver_id' => $truck->driver_id,
                    'driver' => $truck->driver 
                        ? $truck->driver->first_name . ' ' . $truck->driver->last_name
                        : 'Non assigné'
                ];
            })
        );

        $this->info("\nChauffeurs disponibles :");
        $drivers = Driver::whereNull('deleted_at')->get();
        $this->table(
            ['ID', 'Nom', 'Prénom', 'Téléphone'],
            $drivers->map(function ($driver) {
                return [
                    'id' => $driver->id,
                    'last_name' => $driver->last_name,
                    'first_name' => $driver->first_name,
                    'phone' => $driver->phone
                ];
            })
        );
    }
}
