<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    public function index()
    {
        $users = User::with('roles')->latest()->paginate(10);
        return view('admin.users.index', compact('users'));
    }

    public function create()
    {
        $roles = Role::all();
        return view('admin.users.create', compact('roles'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'roles' => 'required|array',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'is_active' => true,
        ]);

        $user->assignRole($request->roles);

        return redirect()->route('admin.users.index')
            ->with('success', 'Utilisateur créé avec succès.');
    }

    public function edit(User $user)
    {
        $roles = Role::all();
        return view('admin.users.edit', compact('user', 'roles'));
    }

    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,'.$user->id,
            'roles' => 'required|array',
        ]);

        $user->update([
            'name' => $request->name,
            'email' => $request->email,
        ]);

        if ($request->filled('password')) {
            $request->validate([
                'password' => 'string|min:8|confirmed',
            ]);
            
            $user->update([
                'password' => Hash::make($request->password),
            ]);
        }

        $user->syncRoles($request->roles);

        return redirect()->route('admin.users.index')
            ->with('success', 'Utilisateur mis à jour avec succès.');
    }

    public function destroy(User $user)
    {
        if ($user->hasRole('admin') && User::role('admin')->count() === 1) {
            return back()->with('error', 'Impossible de supprimer le dernier administrateur.');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'Utilisateur supprimé avec succès.');
    }

    public function toggleActive(User $user)
    {
        try {
            // Log pour debug
            \Log::info('Toggle active called for user: ' . $user->id, [
                'user_id' => $user->id,
                'current_status' => $user->is_active,
                'expects_json' => request()->expectsJson(),
                'headers' => request()->headers->all()
            ]);

            // Vérifier si c'est le dernier admin actif
            if ($user->hasRole('admin') && User::role('admin')->where('is_active', true)->count() === 1 && $user->is_active) {
                $errorMessage = 'Impossible de désactiver le dernier administrateur actif.';

                if (request()->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => $errorMessage
                    ], 422);
                }

                return back()->with('error', $errorMessage);
            }

            // Mettre à jour le statut
            $user->update(['is_active' => !$user->is_active]);

            $successMessage = $user->is_active
                ? 'Utilisateur activé avec succès.'
                : 'Utilisateur désactivé avec succès.';

            // Log du succès
            \Log::info('User status updated successfully', [
                'user_id' => $user->id,
                'new_status' => $user->is_active,
                'message' => $successMessage
            ]);

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $successMessage,
                    'user' => [
                        'id' => $user->id,
                        'is_active' => $user->is_active,
                        'name' => $user->name
                    ]
                ]);
            }

            return back()->with('success', $successMessage);

        } catch (\Exception $e) {
            // Log de l'erreur
            \Log::error('Error in toggleActive: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'exception' => $e->getTraceAsString()
            ]);

            $errorMessage = 'Une erreur est survenue lors de la mise à jour du statut.';

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $errorMessage,
                    'error' => $e->getMessage()
                ], 500);
            }

            return back()->with('error', $errorMessage);
        }
    }
}
