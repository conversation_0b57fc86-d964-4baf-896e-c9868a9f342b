<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\CementOrder;
use App\Models\CementOrderDetail;
use App\Models\Product;
use App\Models\Category;
use App\Models\User;
use App\Models\City;
use App\Models\TruckCapacity;
use App\Models\ProductPrice;
use App\Models\CementOrderNotification;
use App\Models\Driver;
use App\Models\Truck;
use App\Models\TripAssignment;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class CementOrderController extends Controller
{
    public function __construct()
    {
        Log::info('CementOrderController instancié');
    }

    public function index(Request $request)
    {
        try {
            // Préparer la requête de base avec les relations
            $query = CementOrder::with([
                'product',
                'details.supplier',
                'details.destination',
                'creator',
                'validator'
            ])->latest();

            // Appliquer les filtres
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('start_date')) {
                $query->whereDate('created_at', '>=', $request->start_date);
            }

            if ($request->filled('end_date')) {
                $query->whereDate('created_at', '<=', $request->end_date);
            }

            // Récupérer les commandes paginées
            $orders = $query->paginate(10);

            // Calculer les statistiques
            $stats = [
                'total' => CementOrder::count(),
                'pending' => CementOrder::where('status', 'pending')->count(),
                'validated' => CementOrder::where('status', 'validated')->count(),
                'rejected' => CementOrder::where('status', 'rejected')->count()
            ];

            return view('accountant.cement-orders.index', compact('orders', 'stats'));
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des commandes', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return back()->with('error', 'Une erreur est survenue lors de la récupération des commandes.');
        }
    }

    public function create()
    {
        try {
            // Récupérer la catégorie "ciment"
            $cementCategory = Category::where('name', 'LIKE', '%ciment%')
                ->orWhere('slug', 'LIKE', '%ciment%')
                ->first();

            if (!$cementCategory) {
                Log::error('Catégorie ciment non trouvée');
                return back()->with('error', 'La catégorie ciment n\'est pas configurée dans le système.');
            }

            // Récupérer uniquement les produits de type ciment
            $products = Product::where('category_id', $cementCategory->id)
                ->where('is_active', true)
                ->get();

            if ($products->isEmpty()) {
                Log::error('Aucun produit de type ciment trouvé');
                return back()->with('error', 'Aucun produit de type ciment n\'est disponible.');
            }

            // Récupérer les fournisseurs actifs
            $suppliers = Supplier::where('status', 'active')->get();

            // Récupérer toutes les villes actives
            $cities = City::where('is_active', true)
                ->with('region')
                ->get();

            // Récupérer les capacités de camion actives
            $truckCapacities = TruckCapacity::where('is_active', true)
                ->get();

            Log::info('Données chargées pour la vue', [
                'cement_category_id' => $cementCategory->id,
                'products_count' => $products->count(),
                'suppliers_count' => $suppliers->count(),
                'cities_count' => $cities->count(),
                'truck_capacities_count' => $truckCapacities->count()
            ]);

            return view('accountant.cement-orders.create', compact(
                'products',
                'suppliers',
                'cities',
                'truckCapacities'
            ));

        } catch (\Exception $e) {
            Log::error('Erreur dans CementOrderController@create', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return back()->with('error', 'Une erreur est survenue lors du chargement du formulaire.');
        }
    }

    public function store(Request $request)
    {
        try {
            DB::beginTransaction();

            // Validation des données
            $request->validate([
                'reference' => 'required|string|unique:cement_orders,reference',
                'product_id' => 'required|exists:products,id',
                'details' => 'required|array|min:1',
                'details.*.supplier_id' => 'required|exists:suppliers,id',
                'details.*.destination_id' => 'required|exists:cities,id',
                'details.*.quantity' => 'required|numeric|min:0',
                'details.*.unit_price' => 'required|numeric|min:0',
                'details.*.trips_count' => 'required|integer|min:1',
                'details.*.tonnage_per_trip' => 'required|numeric|min:0',
                'notes' => 'nullable|string'
            ]);

            // Calculer le total_tonnage et total_amount
            $total_tonnage = collect($request->details)->sum('quantity');
            $total_amount = collect($request->details)->sum(function($detail) {
                return $detail['quantity'] * $detail['unit_price'];
            });

            // Création du bon de commande
            $cementOrder = CementOrder::create([
                'reference' => $request->reference,
                'product_id' => $request->product_id,
                'created_by' => auth()->id(),
                'notes' => $request->notes,
                'status' => 'pending',
                'total_tonnage' => $total_tonnage,
                'total_amount' => $total_amount,
                'paid_amount' => 0,
                'remaining_amount' => $total_amount
            ]);

            // Ajout des détails du bon de commande
            foreach ($request->details as $index => $detail) {
                $orderDetail = CementOrderDetail::create([
                    'cement_order_id' => $cementOrder->id,
                    'supplier_id' => $detail['supplier_id'],
                    'destination_id' => $detail['destination_id'],
                    'quantity' => $detail['quantity'],
                    'unit_price' => $detail['unit_price'],
                    'trips_count' => $detail['trips_count'],
                    'tonnage_per_trip' => $detail['tonnage_per_trip'],
                    'status' => 'pending'
                ]);

                // Créer une notification pour chaque détail
                CementOrderNotification::create([
                    'cement_order_id' => $cementOrder->id,
                    'cement_order_detail_id' => $orderDetail->id,
                    'title' => 'Nouvelle commande de ciment',
                    'content' => "Une nouvelle commande de {$detail['quantity']} tonnes a été créée pour la destination " . City::find($detail['destination_id'])->name,
                    'type' => 'new_order',
                    'status' => 'unread',
                    'created_by' => auth()->id()
                ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Bon de commande créé avec succès',
                'redirect' => route('accountant.cement-orders.show', $cementOrder)
            ]);

        } catch (ValidationException $e) {
            Log::error('Erreur de validation', [
                'errors' => $e->errors()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Erreur de validation',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de la création du bon de commande', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la création du bon de commande: ' . $e->getMessage()
            ], 500);
        }
    }

    public function show(CementOrder $cement_order)
    {
        $cement_order->load([
            'details.supplier',
            'details.destination',
            'details.tripAssignments.truck',
            'details.tripAssignments.driver',
            'creator',
            'validator'
        ]);

        return view('accountant.cement-orders.show', compact('cement_order'));
    }

    public function calculateTotalTonnage(Request $request)
    {
        Log::info('CementOrderController@calculateTotalTonnage appelé');
        
        try {
            $trips = (int) $request->input('trips', 0);
            $tonnage = (float) $request->input('tonnage', 0);
            
            $totalTonnage = $trips * $tonnage;
            
            Log::info('Tonnage total calculé', [
                'trips' => $trips,
                'tonnage' => $tonnage,
                'total_tonnage' => $totalTonnage
            ]);

            return response()->json([
                'success' => true,
                'total_tonnage' => $totalTonnage
            ]);
            
        } catch (\Exception $e) {
            Log::error('Erreur lors du calcul du tonnage total', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du calcul du tonnage total'
            ], 500);
        }
    }

    public function stats()
    {
        try {
            $monthlyStats = CementOrder::selectRaw('
                MONTH(created_at) as month,
                COUNT(*) as total_orders,
                SUM(total_tonnage) as total_tonnage,
                SUM(total_amount) as total_amount
            ')
            ->where('created_by', auth()->id())
            ->whereYear('created_at', date('Y'))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

            $statusStats = CementOrder::selectRaw('
                status,
                COUNT(*) as count,
                SUM(total_tonnage) as tonnage,
                SUM(total_amount) as amount
            ')
            ->where('created_by', auth()->id())
            ->groupBy('status')
            ->get();

            return view('accountant.cement-orders.partials.stats', [
                'monthlyStats' => $monthlyStats,
                'statusStats' => $statusStats
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des statistiques', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'error' => 'Une erreur est survenue lors de la récupération des statistiques'
            ], 500);
        }
    }

    public function assignVehicle(Request $request, CementOrder $order)
    {
        try {
            $validated = $request->validate([
                'cement_order_detail_id' => 'required|exists:cement_order_details,id',
                'truck_id' => 'required|exists:trucks,id',
                'driver_id' => 'required|exists:drivers,id',
                'trip_number' => 'required|integer|min:1',
                'start_date' => 'required|date_format:Y-m-d\TH:i',
                'end_date' => 'required|date_format:Y-m-d\TH:i|after:start_date',
                'notes' => 'nullable|string',
            ]);

            // Récupérer le détail de la commande
            $detail = CementOrderDetail::findOrFail($validated['cement_order_detail_id']);
            
            // Créer l'affectation
            $assignment = new TripAssignment($validated);
            $assignment->cement_order_id = $order->id;
            $assignment->tonnage = $detail->tonnage_per_trip;
            $assignment->status = 'pending';
            $assignment->save();

            // Mettre à jour le statut du camion et du chauffeur
            $truck = Truck::find($validated['truck_id']);
            $driver = Driver::find($validated['driver_id']);
            
            $truck->update(['status' => 'busy']);
            $driver->update(['status' => 'busy']);

            return redirect()->route('accountant.cement-orders.show', $order)
                ->with('success', 'Affectation créée avec succès');

        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->withErrors(['error' => 'Une erreur est survenue lors de la création de l\'affectation : ' . $e->getMessage()]);
        }
    }

    public function updateAssignmentStatus(Request $request, TripAssignment $assignment)
    {
        try {
            $validated = $request->validate([
                'status' => 'required|in:pending,completed'
            ]);

            $assignment->update([
                'status' => $validated['status'],
                'completed_at' => $validated['status'] === 'completed' ? now() : null
            ]);

            // Si l'affectation est terminée, libérer le camion et le chauffeur
            if ($validated['status'] === 'completed') {
                $assignment->truck->update(['status' => 'available']);
                $assignment->driver->update(['status' => 'available']);
            }

            return back()->with('success', 'Statut de l\'affectation mis à jour avec succès');
        } catch (\Exception $e) {
            return back()->with('error', 'Erreur lors de la mise à jour du statut : ' . $e->getMessage());
        }
    }

    public function destroy(CementOrder $order)
    {
        try {
            if ($order->status !== 'pending') {
                return back()->with('error', 'Seules les commandes en attente peuvent être supprimées.');
            }

            DB::beginTransaction();

            // Supprimer les notifications associées
            CementOrderNotification::where('cement_order_id', $order->id)->delete();

            Log::info('Notifications supprimées', ['order_id' => $order->id]);

            // Supprimer les détails de la commande
            CementOrderDetail::where('cement_order_id', $order->id)->delete();

            Log::info('Détails de commande supprimés', ['order_id' => $order->id]);

            // Supprimer la commande
            $order->delete();

            Log::info('Commande supprimée', ['order_id' => $order->id]);

            DB::commit();

            return back()->with('success', 'Le bon de commande a été supprimé avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de la suppression du bon de commande', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return back()->with('error', 'Une erreur est survenue lors de la suppression du bon de commande.');
        }
    }

    public function getProductPrice(Product $product, City $city)
    {
        try {
            $price = ProductPrice::where('product_id', $product->id)
                ->where('city_id', $city->id)
                ->first();

            if (!$price) {
                return response()->json([
                    'success' => false,
                    'message' => 'Prix non trouvé pour ce produit dans cette ville'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'price' => $price->price
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération du prix', [
                'error' => $e->getMessage(),
                'product_id' => $product->id,
                'city_id' => $city->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération du prix'
            ], 500);
        }
    }

    public function getProductPriceByDestination($productId, $cityId)
    {
        try {
            $price = \App\Models\ProductPrice::where('product_id', $productId)
                ->where('city_id', $cityId)
                ->first();

            if (!$price) {
                return response()->json([
                    'success' => false,
                    'message' => 'Prix non trouvé pour ce produit dans cette ville'
                ]);
            }

            return response()->json([
                'success' => true,
                'price' => $price->price
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération du prix: ' . $e->getMessage()
            ]);
        }
    }
}
