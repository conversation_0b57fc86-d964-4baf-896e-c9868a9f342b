<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CheckModelRoles extends Command
{
    protected $signature = 'roles:check {email}';
    protected $description = 'Check roles in model_has_roles table';

    public function handle()
    {
        $email = $this->argument('email');
        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("Utilisateur non trouvé : {$email}");
            return 1;
        }

        $this->info("\nInformations de l'utilisateur :");
        $this->info("--------------------------------");
        $this->info("ID : " . $user->id);
        $this->info("Nom : " . $user->name);
        $this->info("Email : " . $user->email);

        $this->info("\nRôles dans model_has_roles :");
        $this->info("--------------------------------");
        $roles = DB::table('model_has_roles')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('model_has_roles.model_id', $user->id)
            ->where('model_has_roles.model_type', User::class)
            ->get(['roles.id', 'roles.name']);

        foreach ($roles as $role) {
            $this->info("- Role ID: {$role->id}, Name: {$role->name}");
        }

        $this->info("\nTous les rôles disponibles :");
        $this->info("--------------------------------");
        $allRoles = DB::table('roles')->get();
        foreach ($allRoles as $role) {
            $this->info("- Role ID: {$role->id}, Name: {$role->name}");
        }

        return 0;
    }
}
