<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\User;
use App\Models\Supply;
use App\Models\Sale;
use App\Models\Payment;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class AccountantDashboardController extends Controller
{
    /**
     * Calcule toutes les statistiques nécessaires pour le tableau de bord
     *
     * @return array
     */
    private function calculateDashboardStats()
    {
        $currentMonth = Carbon::now()->month;
        $currentYear = Carbon::now()->year;
        
        // Commandes de ciment du mois en cours
        $monthlyCementOrders = Sale::whereMonth('created_at', $currentMonth)
                                ->whereYear('created_at', $currentYear)
                                ->whereHas('saleItems', function($query) {
                                    $query->whereHas('product', function($q) {
                                        $q->where('name', 'like', '%ciment%');
                                    });
                                })
                                ->count();
        
        // Nombre total de commandes de ciment
        $totalCementOrders = Sale::whereHas('saleItems', function($query) {
            $query->whereHas('product', function($q) {
                $q->where('name', 'like', '%ciment%');
            });
        })->count();
        
        return [
            'monthly_cement_orders' => $monthlyCementOrders,
            'total_cement_orders' => $totalCementOrders,
            'monthly_revenue' => Sale::whereMonth('created_at', $currentMonth)
                                ->whereYear('created_at', $currentYear)
                                ->sum('total_amount'),
            'monthly_payments' => Payment::whereMonth('created_at', $currentMonth)
                                ->whereYear('created_at', $currentYear)
                                ->sum('amount'),
            'active_customers' => DB::table('sales')
                                ->join('customers', 'sales.customer_id', '=', 'customers.id')
                                ->whereMonth('sales.created_at', $currentMonth)
                                ->whereYear('sales.created_at', $currentYear)
                                ->distinct('customers.id')
                                ->count('customers.id')
        ];
    }
    /**
     * Affiche le tableau de bord comptable classique
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Rediriger directement vers le nouveau tableau de bord professionnel
        return redirect()->route('accountant.dashboard.professional');
    }
    
    /**
     * Calcule les statistiques pour le mois courant
     * 
     * @return array
     */
    private function getMonthlyStats()
    {
        $currentMonth = Carbon::now()->month;
        $currentYear = Carbon::now()->year;
        
        // Commandes de ciment du mois en cours
        $monthlyCementOrders = Sale::whereMonth('created_at', $currentMonth)
                                ->whereYear('created_at', $currentYear)
                                ->whereHas('saleItems', function($query) {
                                    $query->whereHas('product', function($q) {
                                        $q->where('name', 'like', '%ciment%');
                                    });
                                })
                                ->count();
                                
        // Autres statistiques pour le tableau de bord
        return [
                'monthly_cement_orders' => $monthlyCementOrders,
                'monthly_revenue' => Sale::whereMonth('created_at', $currentMonth)
                                    ->whereYear('created_at', $currentYear)
                                    ->sum('total_amount'),
                'monthly_payments' => Payment::whereMonth('created_at', $currentMonth)
                                    ->whereYear('created_at', $currentYear)
                                    ->sum('amount'),
                'active_customers' => DB::table('sales')
                                    ->join('customers', 'sales.customer_id', '=', 'customers.id')
                                    ->whereMonth('sales.created_at', $currentMonth)
                                    ->whereYear('sales.created_at', $currentYear)
                                    ->distinct('customers.id')
                                    ->count('customers.id')
            ];
            
            // Débogage - Afficher les valeurs des variables
            if (request()->has('debug')) {
                dd([
                    'totalSales' => $totalSales,
                    'totalRevenue' => $totalRevenue,
                    'totalSupplies' => $totalSupplies,
                    'validatedSupplies' => $validatedSupplies,
                    'pendingSupplies' => $pendingSupplies,
                    'rejectedSupplies' => $rejectedSupplies,
                    'totalSupplyTonnage' => $totalSupplyTonnage
                ]);
            }
            
            return view('accountant.dashboard', compact(
                'totalSales', 'totalRevenue', 'totalPayments', 'pendingPayments',
                'totalInvoices', 'paidInvoices', 'partialInvoices', 'unpaidInvoices',
                'recentSales', 'stats', 'totalSupplies', 'totalSupplyAmount', 'pendingSupplies', 
                'validatedSupplies', 'rejectedSupplies', 'totalSupplyTonnage'
            ));
            
        } catch (\Exception $e) {
            Log::error('Erreur dans le tableau de bord comptable: ' . $e->getMessage());
            
            // Définir des valeurs par défaut pour les statistiques en cas d'erreur
            $stats = [
                'monthly_cement_orders' => 0,
                'total_cement_orders' => 0,
                'monthly_revenue' => 0,
                'monthly_payments' => 0,
                'active_customers' => 0
            ];
            
            // Définir des valeurs par défaut pour les variables d'approvisionnement
            $totalSales = 0;
            $totalRevenue = 0;
            $totalPayments = 0;
            $pendingPayments = 0;
            $totalInvoices = 0;
            $paidInvoices = 0;
            $partialInvoices = 0;
            $unpaidInvoices = 0;
            $recentSales = collect([]);
            $totalSupplies = 0;
            $totalSupplyAmount = 0;
            $pendingSupplies = 0;
            $validatedSupplies = 0;
            $rejectedSupplies = 0;
            $totalSupplyTonnage = 0;
            
            return view('accountant.dashboard', compact(
                'totalSales', 'totalRevenue', 'totalPayments', 'pendingPayments',
                'totalInvoices', 'paidInvoices', 'partialInvoices', 'unpaidInvoices',
                'recentSales', 'stats', 'totalSupplies', 'totalSupplyAmount', 'pendingSupplies', 
                'validatedSupplies', 'rejectedSupplies', 'totalSupplyTonnage'
            ))->with('error', 'Une erreur est survenue lors du chargement des données du tableau de bord.');
        }
    }
    
    /**
     * Affiche le tableau de bord comptable moderne avec des graphiques et animations
     *
     * @return \Illuminate\View\View
     */
    public function modernDashboard()
    {
        try {
            // Statistiques des ventes et paiements
            $totalSales = Sale::count();
            $totalRevenue = Sale::sum('total_amount');
            $totalPayments = Payment::sum('amount');
            $pendingPayments = Sale::whereIn('payment_status', ['unpaid', 'partial'])->sum('total_amount') - 
                              Payment::whereHas('sale', function($query) {
                                  $query->whereIn('payment_status', ['unpaid', 'partial']);
                              })->sum('amount');
                              
            // Statistiques des approvisionnements
            $totalSupplies = Supply::count();
            $totalSupplyAmount = Supply::sum('total_amount');
            $pendingSupplies = Supply::where('status', 'pending')->count();
            $validatedSupplies = Supply::where('status', 'validated')->count();
            $rejectedSupplies = Supply::where('status', 'rejected')->count();
            $totalSupplyTonnage = Supply::sum('total_tonnage');
            
            // Statistiques des factures par statut de paiement
            $totalInvoices = Sale::count();
            $paidInvoices = Sale::where('payment_status', 'paid')->count();
            $partialInvoices = Sale::where('payment_status', 'partial')->count();
            $unpaidInvoices = Sale::where('payment_status', 'unpaid')->count();
            
            // Récupération des ventes récentes
            $recentSales = Sale::with('customer')
                            ->latest()
                            ->take(5)
                            ->get();
            
            // Statistiques supplémentaires pour le tableau de bord
            $currentMonth = Carbon::now()->month;
            $currentYear = Carbon::now()->year;
            
            // Commandes de ciment du mois en cours
            $monthlyCementOrders = Sale::whereMonth('created_at', $currentMonth)
                                    ->whereYear('created_at', $currentYear)
                                    ->whereHas('saleItems', function($query) {
                                        $query->whereHas('product', function($q) {
                                            $q->where('name', 'like', '%ciment%');
                                        });
                                    })
                                    ->count();
            
            // Autres statistiques pour le tableau de bord
            $stats = [
                'monthly_cement_orders' => $monthlyCementOrders,
                'monthly_revenue' => Sale::whereMonth('created_at', $currentMonth)
                                    ->whereYear('created_at', $currentYear)
                                    ->sum('total_amount'),
                'monthly_payments' => Payment::whereMonth('created_at', $currentMonth)
                                    ->whereYear('created_at', $currentYear)
                                    ->sum('amount'),
                'active_customers' => DB::table('sales')
                                    ->join('customers', 'sales.customer_id', '=', 'customers.id')
                                    ->whereMonth('sales.created_at', $currentMonth)
                                    ->whereYear('sales.created_at', $currentYear)
                                    ->distinct('customers.id')
                                    ->count('customers.id')
            ];
            
            // Traiter les ventes récentes pour ajouter des informations supplémentaires
            foreach ($recentSales as $sale) {
                // S'assurer que le numéro de facture existe
                if (empty($sale->invoice_number)) {
                    $sale->invoice_number = 'SALE-' . $sale->id;
                }
                
                // Calculer le montant payé si nécessaire
                if (!isset($sale->amount_paid)) {
                    $sale->amount_paid = $sale->payments->sum('amount') ?? 0;
                }
                
                // Calculer le pourcentage de paiement
                $sale->payment_percentage = ($sale->total_amount > 0) ? 
                    ($sale->amount_paid / $sale->total_amount) * 100 : 0;
            }
            
            // Récupérer les activités récentes
            $recentActivities = $this->getRecentActivities();
            
            // Préparation des données pour les graphiques
            $monthlySales = $this->getMonthlySalesData();
            $paymentStats = $this->getPaymentStatusData();
            
            // Préparation des données pour le graphique des approvisionnements
            $supplyChartData = $this->prepareSupplyChartData();
            
            // Encodage des données pour les graphiques JavaScript
            $chartStats = json_encode([
                'sales' => [
                    'monthlySales' => $monthlySales,
                    'paymentStatus' => $paymentStats
                ],
                'supplies' => $supplyChartData
            ]);
            
        } catch (\Exception $e) {
            // En cas d'erreur, initialiser des valeurs par défaut
            $totalSales = 0;
            $totalRevenue = 0;
            $totalPayments = 0;
            $pendingPayments = 0;
            $totalInvoices = 0;
            $paidInvoices = 0;
            $partialInvoices = 0;
            $unpaidInvoices = 0;
            $recentSales = collect([]);
            $monthlySales = ['labels' => [], 'data' => []];
            $paymentStats = ['paid' => 0, 'partial' => 0, 'unpaid' => 0];
            $recentActivities = [];
            $stats = [
                'monthly_cement_orders' => 0,
                'total_cement_orders' => 0,
                'monthly_revenue' => 0,
                'monthly_payments' => 0,
                'active_customers' => 0
            ];
            $totalSupplies = 0;
            $totalSupplyAmount = 0;
            $mySupplies = 0;
            $pendingSupplies = 0;
            $validatedSupplies = 0;
            $rejectedSupplies = 0;
            $totalSupplyTonnage = 0;
            $supplyChartData = ['labels' => [], 'supplies' => [], 'tonnages' => []];
            $chartStats = json_encode(['sales' => ['monthlySales' => [], 'paymentStatus' => []], 'supplies' => []]);
            
            // Log de l'erreur
            Log::error('Erreur dans le tableau de bord comptable: ' . $e->getMessage());
        }
        
        // Vérifier si la vue existe
        if (!view()->exists('accountant.dashboard-modern')) {
            Log::error('Vue dashboard-modern introuvable');
            return view('accountant.dashboard', compact(
                'totalSales', 'totalRevenue', 'totalPayments', 'pendingPayments',
                'totalInvoices', 'paidInvoices', 'partialInvoices', 'unpaidInvoices',
                'recentSales', 'stats'
            ))->with('error', 'La vue moderne est en cours de développement.');
        }
        
        // Rendu de la vue avec toutes les données
        return view('accountant.dashboard-modern', compact(
            'totalSales', 'totalRevenue', 'totalPayments', 'pendingPayments',
            'totalInvoices', 'paidInvoices', 'partialInvoices', 'unpaidInvoices',
            'recentSales', 'monthlySales', 'paymentStats', 'recentActivities', 'stats',
            'totalSupplies', 'totalSupplyAmount', 'mySupplies', 'pendingSupplies', 'validatedSupplies', 'rejectedSupplies', 'totalSupplyTonnage',
            'chartStats', 'supplyChartData'
        ));
    }

    /**
     * Affiche le tableau de bord comptable professionnel avec design moderne et animations
     *
     * @return \Illuminate\View\View
     */
    public function professionalDashboard()
    {
        try {
            // Statistiques des ventes et paiements
            $totalSales = Sale::count();
            $totalRevenue = Sale::sum('total_amount');
            $totalPayments = Payment::sum('amount');
            $pendingPayments = Sale::whereIn('payment_status', ['unpaid', 'partial'])->sum('total_amount') - 
                              Payment::whereHas('sale', function($query) {
                                  $query->whereIn('payment_status', ['unpaid', 'partial']);
                              })->sum('amount');
                              
            // Statistiques des approvisionnements
            $totalSupplies = Supply::count();
            $totalSupplyAmount = Supply::sum('total_amount');
            $pendingSupplies = Supply::where('status', 'pending')->count();
            $validatedSupplies = Supply::where('status', 'validated')->count();
            $rejectedSupplies = Supply::where('status', 'rejected')->count();
            $totalSupplyTonnage = Supply::sum('total_tonnage');
            
            // Statistiques des factures par statut de paiement
            $totalInvoices = Sale::count();
            $paidInvoices = Sale::where('payment_status', 'paid')->count();
            $partialInvoices = Sale::where('payment_status', 'partial')->count();
            $unpaidInvoices = Sale::where('payment_status', 'unpaid')->count();
            
            // Récupération des ventes récentes
            $recentSales = Sale::with('customer')
                            ->latest()
                            ->take(5)
                            ->get();
            
            // Statistiques supplémentaires pour le tableau de bord
            $currentMonth = Carbon::now()->month;
            $currentYear = Carbon::now()->year;
            
            // Commandes de ciment du mois en cours
            $monthlyCementOrders = Sale::whereMonth('created_at', $currentMonth)
                                    ->whereYear('created_at', $currentYear)
                                    ->whereHas('saleItems', function($query) {
                                        $query->whereHas('product', function($q) {
                                            $q->where('name', 'like', '%ciment%');
                                        });
                                    })
                                    ->count();
            
            // Autres statistiques pour le tableau de bord
            $stats = [
                'monthly_cement_orders' => $monthlyCementOrders,
                'monthly_revenue' => Sale::whereMonth('created_at', $currentMonth)
                                    ->whereYear('created_at', $currentYear)
                                    ->sum('total_amount'),
                'monthly_payments' => Payment::whereMonth('created_at', $currentMonth)
                                    ->whereYear('created_at', $currentYear)
                                    ->sum('amount'),
                'active_customers' => DB::table('sales')
                                    ->join('customers', 'sales.customer_id', '=', 'customers.id')
                                    ->whereMonth('sales.created_at', $currentMonth)
                                    ->whereYear('sales.created_at', $currentYear)
                                    ->distinct('customers.id')
                                    ->count('customers.id')
            ];
            
            // Récupérer les activités récentes
            $recentActivities = $this->getRecentActivities();
            
            // Préparation des données pour les graphiques
            $monthlySales = $this->getMonthlySalesData();
            $paymentStats = $this->getPaymentStatusData();
            $supplyChartData = $this->prepareSupplyChartData();
            
        } catch (\Exception $e) {
            // En cas d'erreur, initialiser des valeurs par défaut
            $totalSales = 0;
            $totalRevenue = 0;
            $totalPayments = 0;
            $pendingPayments = 0;
            $totalInvoices = 0;
            $paidInvoices = 0;
            $partialInvoices = 0;
            $unpaidInvoices = 0;
            $recentSales = collect([]);
            $monthlySales = ['labels' => [], 'data' => []];
            $paymentStats = ['paid' => 0, 'partial' => 0, 'unpaid' => 0];
            $recentActivities = [];
            $stats = [
                'monthly_cement_orders' => 0,
                'monthly_revenue' => 0,
                'monthly_payments' => 0,
                'active_customers' => 0
            ];
            $totalSupplies = 0;
            $totalSupplyAmount = 0;
            $pendingSupplies = 0;
            $validatedSupplies = 0;
            $rejectedSupplies = 0;
            $totalSupplyTonnage = 0;
            $supplyChartData = [];
            
            // Log de l'erreur
            Log::error('Erreur dans le tableau de bord comptable professionnel: ' . $e->getMessage());
        }
        
        // Vérifier si la vue existe
        if (!view()->exists('accountant.dashboard-professional')) {
            Log::error('Vue dashboard-professional introuvable');
            return view('accountant.dashboard', compact(
                'totalSales', 'totalRevenue', 'totalPayments', 'pendingPayments',
                'totalInvoices', 'paidInvoices', 'partialInvoices', 'unpaidInvoices',
                'recentSales', 'stats'
            ))->with('error', 'La vue professionnelle est en cours de développement.');
        }
        
        // Rendu de la vue avec toutes les données
        return view('accountant.dashboard-professional', compact(
            'totalSales', 'totalRevenue', 'totalPayments', 'pendingPayments',
            'totalInvoices', 'paidInvoices', 'partialInvoices', 'unpaidInvoices',
            'recentSales', 'monthlySales', 'paymentStats', 'recentActivities', 'stats',
            'totalSupplies', 'totalSupplyAmount', 'pendingSupplies', 'validatedSupplies', 'rejectedSupplies', 'totalSupplyTonnage',
            'supplyChartData'
        ));
    }

    /**
     * Récupère les données des ventes mensuelles pour le graphique
     *
     * @param Carbon|null $startDate Date de début pour filtrer les données
     * @return array
     */
    private function getMonthlySalesData($startDate = null)
    {
        $months = [];
        $sales = [];
        
        // Déterminer la période à afficher en fonction de la date de début
        $periodCount = 6; // Par défaut, 6 derniers mois
        
        if ($startDate) {
            $now = Carbon::now();
            $diffInMonths = $startDate->diffInMonths($now);
            
            // Si la période est inférieure à 6 mois, ajuster le nombre de mois à afficher
            if ($diffInMonths < 6) {
                $periodCount = max(1, $diffInMonths + 1);
            }
        }
        
        for ($i = $periodCount - 1; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $months[] = $month->format('M Y');
            
            $query = Sale::whereYear('created_at', $month->year)
                      ->whereMonth('created_at', $month->month);
            
            // Appliquer le filtre de date si nécessaire
            if ($startDate && $month->lt($startDate)) {
                $sales[] = 0;
            } else {
                $sales[] = $query->sum('total_amount');
            }
        }
        
        return [
            'labels' => $months,
            'data' => $sales
        ];
    }
    
    /**
     * Récupère les données des statuts de paiement pour le graphique en donut
     *
     * @param Carbon|null $startDate Date de début pour filtrer les données
     * @return array
     */
    private function getPaymentStatusData($startDate = null)
    {
        $query = Sale::query();
        
        // Appliquer le filtre de date si nécessaire
        if ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }
        
        $paidCount = $query->clone()->where('payment_status', 'paid')->count();
        $partialCount = $query->clone()->where('payment_status', 'partial')->count();
        $unpaidCount = $query->clone()->where('payment_status', 'unpaid')->count();
        
        return [
            'labels' => ['Payé', 'Partiel', 'Non payé'],
            'data' => [$paidCount, $partialCount, $unpaidCount],
            'colors' => ['#4CAF50', '#FF9800', '#F44336']
        ];
    }
    
    /**
     * Récupère les données des taux de recouvrement pour le graphique en barres
     *
     * @param Carbon|null $startDate Date de début pour filtrer les données
     * @return array
     */
    private function getRecoveryRateData($startDate = null)
    {
        $months = [];
        $rates = [];
        
        // Déterminer la période à afficher en fonction de la date de début
        $periodCount = 6; // Par défaut, 6 derniers mois
        
        if ($startDate) {
            $now = Carbon::now();
            $diffInMonths = $startDate->diffInMonths($now);
            
            // Si la période est inférieure à 6 mois, ajuster le nombre de mois à afficher
            if ($diffInMonths < 6) {
                $periodCount = max(1, $diffInMonths + 1);
            }
        }
        
        for ($i = $periodCount - 1; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $months[] = $month->format('M Y');
            
            // Appliquer le filtre de date si nécessaire
            if ($startDate && $month->lt($startDate)) {
                $rates[] = 0;
                continue;
            }
            
            $totalSales = Sale::whereYear('created_at', $month->year)
                         ->whereMonth('created_at', $month->month)
                         ->sum('total_amount');
            
            $totalPayments = Payment::whereHas('sale', function($query) use ($month) {
                                $query->whereYear('created_at', $month->year)
                                      ->whereMonth('created_at', $month->month);
                             })
                             ->sum('amount');
            
            // Calculer le taux de recouvrement (en pourcentage)
            $rate = ($totalSales > 0) ? min(100, ($totalPayments / $totalSales) * 100) : 0;
            $rates[] = round($rate, 2);
        }
        
        return [
            'labels' => $months,
            'data' => $rates
        ];
    }
    
    /**
     * Prépare les données pour les graphiques du tableau de bord
     * 
     * @return array
     */
    private function prepareChartData()
    {
        $monthlySalesData = $this->getMonthlySalesData();
        $paymentStatusData = $this->getPaymentStatusData();
        $recoveryRateData = $this->getRecoveryRateData();
        
        return [
            'monthlySales' => $monthlySalesData,
            'paymentStatus' => $paymentStatusData,
            'recoveryRate' => $recoveryRateData
        ];
    }
    
    /**
     * Prépare les données pour le graphique des approvisionnements
     * 
     * @return array
     */
    private function prepareSupplyChartData()
    {
        $months = [];
        $supplies = [];
        $tonnages = [];
        
        // Récupérer les données des 6 derniers mois
        for ($i = 5; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $months[] = $month->format('M Y');
            
            $monthlySupplies = Supply::whereYear('created_at', $month->year)
                                ->whereMonth('created_at', $month->month);
            
            $supplies[] = $monthlySupplies->count();
            $tonnages[] = $monthlySupplies->sum('total_tonnage');
        }
        
        return [
            'labels' => $months,
            'supplies' => $supplies,
            'tonnages' => $tonnages
        ];
    }

    /**
     * Récupère les activités récentes pour le tableau de bord
     *
     * @param Carbon|null $startDate Date de début pour filtrer les données
     * @return array
     */
    private function getRecentActivities($startDate = null)
    {
        $activities = [];
        
        try {
            // Récupération des paiements récents
            $paymentsQuery = Payment::with('sale')->latest();
            
            // Appliquer le filtre de date si nécessaire
            if ($startDate) {
                $paymentsQuery->where('created_at', '>=', $startDate);
            }
            
            $recentPayments = $paymentsQuery->take(3)->get();
            
            foreach ($recentPayments as $payment) {
                if ($payment->sale) {
                    $activities[] = (object) [
                        'type' => 'success',
                        'created_at' => $payment->created_at,
                        'title' => 'Nouveau paiement reçu',
                        'badge' => 'success',
                        'badge_text' => number_format($payment->amount) . ' F',
                        'description' => 'Paiement pour la facture #' . ($payment->sale->invoice_number ?? 'SALE-'.$payment->sale_id)
                    ];
                }
            }
            
            // Récupération des ventes récentes
            $saleIds = $recentPayments->pluck('sale_id')->filter()->toArray();
            $salesQuery = Sale::latest()
                          ->when(!empty($saleIds), function($query) use ($saleIds) {
                              return $query->whereNotIn('id', $saleIds);
                          });
            
            // Appliquer le filtre de date si nécessaire
            if ($startDate) {
                $salesQuery->where('created_at', '>=', $startDate);
            }
            
            $recentSales = $salesQuery->take(3)->get();
            
            foreach ($recentSales as $sale) {
                $badgeType = 'primary';
                $badgeText = 'Nouvelle';
                
                if ($sale->payment_status == 'paid' || $sale->payment_status == 'completed') {
                    $badgeType = 'success';
                    $badgeText = 'Payée';
                } elseif ($sale->payment_status == 'partial') {
                    $badgeType = 'warning';
                    $badgeText = 'Partiel';
                } else {
                    $badgeType = 'danger';
                    $badgeText = 'Impayée';
                }
                
                $activities[] = (object) [
                    'type' => 'primary',
                    'created_at' => $sale->created_at,
                    'title' => 'Nouvelle vente enregistrée',
                    'badge' => $badgeType,
                    'badge_text' => $badgeText,
                    'description' => 'Vente #' . ($sale->invoice_number ?? 'SALE-'.$sale->id) . ' de ' . number_format($sale->total_amount) . ' F'
                ];
            }
            
            // Tri des activités par date
            usort($activities, function($a, $b) {
                return $b->created_at <=> $a->created_at;
            });
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des activités récentes: ' . $e->getMessage());
        }
        
        return $activities;
    }

    /**
     * Récupère les données du tableau de bord pour les requêtes AJAX des filtres de période
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDashboardData(Request $request)
    {
        // Récupérer la période demandée (today, week, month, quarter, year, all)
        $period = $request->input('period', 'all');
        $startDate = null;
        
        // Déterminer la date de début en fonction de la période
        if ($period !== 'all') {
            $now = now();
            switch ($period) {
                case 'today':
                    $startDate = $now->startOfDay();
                    break;
                case 'week':
                    $startDate = $now->startOfWeek();
                    break;
                case 'month':
                    $startDate = $now->startOfMonth();
                    break;
                case 'quarter':
                    $startDate = $now->startOfQuarter();
                    break;
                case 'year':
                    $startDate = $now->startOfYear();
                    break;
            }
        }
        
        // Récupérer les données filtrées
        $statistics = $this->getFilteredStatistics($startDate);
        $chartData = [
            'monthlySales' => $this->getMonthlySalesData($startDate),
            'paymentStatus' => $this->getPaymentStatusData($startDate),
            'recoveryRate' => $this->getRecoveryRateData($startDate)
        ];
        $tablesData = [
            'recentSales' => $this->getRecentSales($startDate),
            'recentPayments' => $this->getRecentPayments($startDate)
        ];
        
        // Renvoyer les données au format JSON
        return response()->json([
            'success' => true,
            'statistics' => $statistics,
            'chartData' => $chartData,
            'tablesData' => $tablesData
        ]);
    }
    

    
    /**
     * Récupère les statistiques filtrées par période
     * 
     * @param Carbon|null $startDate
     * @return array
     */
    private function getFilteredStatistics($startDate = null)
    {
        // Récupérer l'ID du comptable connecté
        $accountantId = auth()->id();
        
        // Requêtes de base
        $salesQuery = Sale::query();
        $paymentsQuery = Payment::query();
        $supplyQuery = Supply::query();
        
        // Appliquer le filtre de date si nécessaire
        if ($startDate) {
            $salesQuery->where('created_at', '>=', $startDate);
            $paymentsQuery->where('created_at', '>=', $startDate);
            $supplyQuery->where('created_at', '>=', $startDate);
        }
        
        // Statistiques des ventes
        $totalSales = $salesQuery->count();
        $totalRevenue = $salesQuery->sum('total_amount');
        
        // Statistiques des paiements
        $totalPayments = $paymentsQuery->sum('amount');
        $pendingPayments = $totalRevenue - $totalPayments;
        if ($pendingPayments < 0) $pendingPayments = 0;
        
        // Statistiques des factures (utiliser les ventes comme factures)
        $totalInvoices = $salesQuery->count();
        $paidInvoices = $salesQuery->clone()->where('payment_status', 'paid')->count();
        $partialInvoices = $salesQuery->clone()->where('payment_status', 'partial')->count();
        $unpaidInvoices = $salesQuery->clone()->where('payment_status', 'unpaid')->count();
        
        // Statistiques des approvisionnements
        $totalSupplies = $supplyQuery->count();
        
        // Approvisionnements traités par le comptable connecté
        $mySuppliesQuery = clone $supplyQuery;
        $mySupplies = $mySuppliesQuery->where('created_by', $accountantId)->count();
        
        // Cloner les requêtes pour éviter les conflits avec where
        $validatedQuery = clone $supplyQuery;
        $rejectedQuery = clone $supplyQuery;
        $pendingQuery = clone $supplyQuery;
        
        // Compter les approvisionnements par statut
        $validatedSupplies = $validatedQuery->where('status', 'validated')->count();
        $rejectedSupplies = $rejectedQuery->where('status', 'rejected')->count();
        $pendingSupplies = $pendingQuery->where('status', 'pending')->count();
        
        // Calculer les tonnages par statut
        $totalSupplyTonnage = $supplyQuery->sum('total_tonnage');
        $validatedSupplyTonnage = $validatedQuery->where('status', 'validated')->sum('total_tonnage');
        $rejectedSupplyTonnage = $rejectedQuery->where('status', 'rejected')->sum('total_tonnage');
        $pendingSupplyTonnage = $pendingQuery->where('status', 'pending')->sum('total_tonnage');
        
        return [
            'totalSales' => $totalSales,
            'totalRevenue' => $totalRevenue,
            'totalPayments' => $totalPayments,
            'pendingPayments' => $pendingPayments,
            'totalInvoices' => $totalInvoices,
            'paidInvoices' => $paidInvoices,
            'partialInvoices' => $partialInvoices,
            'unpaidInvoices' => $unpaidInvoices,
            'totalSupplies' => $totalSupplies,
            'mySupplies' => $mySupplies,
            'validatedSupplies' => $validatedSupplies,
            'rejectedSupplies' => $rejectedSupplies,
            'pendingSupplies' => $pendingSupplies,
            'totalSupplyTonnage' => round($totalSupplyTonnage, 2),
            'validatedSupplyTonnage' => round($validatedSupplyTonnage, 2),
            'rejectedSupplyTonnage' => round($rejectedSupplyTonnage, 2),
            'pendingSupplyTonnage' => round($pendingSupplyTonnage, 2)
        ];
    }
    
    /**
     * Récupère les ventes récentes filtrées par période
     * 
     * @param Carbon|null $startDate
     * @return Collection
     */
    private function getRecentSales($startDate = null)
    {
        $query = Sale::with('customer')->latest();
        
        if ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }
        
        return $query->take(5)->get();
    }
    
    /**
     * Récupère les paiements récents filtrés par période
     * 
     * @param Carbon|null $startDate
     * @return Collection
     */
    private function getRecentPayments($startDate = null)
    {
        $query = Payment::with('sale')->latest();
        
        if ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }
        
        return $query->take(5)->get();
    }
    
    /**
     * Récupère les statistiques pour les requêtes AJAX
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats(Request $request)
    {
        try {
            Log::info('Début du chargement des statistiques du tableau de bord');
            
            $period = $request->input('period', 7);
            $endDate = Carbon::now();
            $startDate = Carbon::now()->subDays($period);
            
            Log::info('Période sélectionnée', [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
                'period' => $period
            ]);

            // Statistiques globales
            Log::info('Chargement des statistiques globales');
            
            // Statistiques des ventes et paiements
            $totalSales = Sale::whereBetween('created_at', [$startDate, $endDate])->count();
            $totalRevenue = Sale::whereBetween('created_at', [$startDate, $endDate])->sum('total_amount');
            $totalPayments = Payment::whereBetween('created_at', [$startDate, $endDate])->sum('amount');
            $pendingPayments = $totalRevenue - $totalPayments;
            if ($pendingPayments < 0) $pendingPayments = 0;
            
            // Statistiques des factures (utiliser les ventes comme factures)
            $totalInvoices = $totalSales;
            $paidInvoices = Sale::whereBetween('created_at', [$startDate, $endDate])->where('payment_status', 'paid')->count();
            $partialInvoices = Sale::whereBetween('created_at', [$startDate, $endDate])->where('payment_status', 'partial')->count();
            $unpaidInvoices = Sale::whereBetween('created_at', [$startDate, $endDate])->where('payment_status', 'unpaid')->count();
            
            // Statistiques des approvisionnements
            $totalSupplies = Supply::whereBetween('created_at', [$startDate, $endDate])->count();
            
            // Récupérer l'ID du comptable connecté
            $accountantId = auth()->id();
            $mySupplies = Supply::whereBetween('created_at', [$startDate, $endDate])->where('created_by', $accountantId)->count();
            
            $validatedSupplies = Supply::whereBetween('created_at', [$startDate, $endDate])->where('status', 'validated')->count();
            $rejectedSupplies = Supply::whereBetween('created_at', [$startDate, $endDate])->where('status', 'rejected')->count();
            $pendingSupplies = Supply::whereBetween('created_at', [$startDate, $endDate])->where('status', 'pending')->count();
            
            // Tonnages
            $totalSupplyTonnage = Supply::whereBetween('created_at', [$startDate, $endDate])->sum('total_tonnage');
            $validatedSupplyTonnage = Supply::whereBetween('created_at', [$startDate, $endDate])->where('status', 'validated')->sum('total_tonnage');
            $rejectedSupplyTonnage = Supply::whereBetween('created_at', [$startDate, $endDate])->where('status', 'rejected')->sum('total_tonnage');
            $pendingSupplyTonnage = Supply::whereBetween('created_at', [$startDate, $endDate])->where('status', 'pending')->sum('total_tonnage');
            
            $stats = [
                'totalSales' => $totalSales,
                'totalRevenue' => $totalRevenue,
                'totalPayments' => $totalPayments,
                'pendingPayments' => $pendingPayments,
                'totalInvoices' => $totalInvoices,
                'paidInvoices' => $paidInvoices,
                'partialInvoices' => $partialInvoices,
                'unpaidInvoices' => $unpaidInvoices,
                'totalSupplies' => $totalSupplies,
                'mySupplies' => $mySupplies,
                'validatedSupplies' => $validatedSupplies,
                'rejectedSupplies' => $rejectedSupplies,
                'pendingSupplies' => $pendingSupplies,
                'totalSupplyTonnage' => round($totalSupplyTonnage, 2),
                'validatedSupplyTonnage' => round($validatedSupplyTonnage, 2),
                'rejectedSupplyTonnage' => round($rejectedSupplyTonnage, 2),
                'pendingSupplyTonnage' => round($pendingSupplyTonnage, 2)
            ];
            Log::info('Statistiques globales chargées', $stats);

            // Données pour les graphiques
            $chartData = $this->prepareChartData();
            
            // Données pour le graphique des approvisionnements
            $supplyChartData = $this->prepareSupplyChartData();
            
            $stats = json_encode([
                'sales' => $chartData,
                'supplies' => $supplyChartData
            ]);

            // Données pour le graphique
            Log::info('Chargement des données du graphique');
            $ordersData = Supply::select(DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as count'))
                ->whereBetween('created_at', [
                    $startDate->format('Y-m-d 00:00:00'),
                    $endDate->format('Y-m-d 23:59:59')
                ])
                ->groupBy(DB::raw('DATE(created_at)'))
                ->get();
            Log::info('Données brutes du graphique récupérées', ['count' => $ordersData->count()]);

            $labels = [];
            $data = [];
            
            for ($date = $startDate->copy(); $date <= $endDate; $date->addDay()) {
                $labels[] = $date->format('d/m');
                $count = $ordersData->firstWhere('date', $date->format('Y-m-d'));
                $data[] = $count ? $count->count : 0;
            }

            $chartData = [
                'labels' => $labels,
                'data' => $data
            ];
            Log::info('Données du graphique préparées', ['labels_count' => count($labels)]);

            // Derniers approvisionnements
            Log::info('Chargement des derniers approvisionnements');
            $latestOrders = Supply::with(['supplier'])
                ->latest()
                ->take(5)
                ->get();
            
            if ($latestOrders->isEmpty()) {
                Log::warning('Aucun approvisionnement trouvé');
            }

            $formattedOrders = $latestOrders->map(function ($supply) {
                try {
                    return [
                        'id' => $supply->id,
                        'reference' => $supply->reference,
                        'date' => $supply->created_at->format('d/m/Y'),
                        'client' => $supply->supplier->name,
                        'tonnage' => $supply->total_tonnage,
                        'status' => $supply->status,
                    ];
                } catch (\Exception $e) {
                    Log::error('Erreur lors du formatage d\'un approvisionnement', [
                        'supply_id' => $supply->id,
                        'error' => $e->getMessage()
                    ]);
                    return null;
                }
            })->filter();
            
            Log::info('Derniers approvisionnements chargés', ['count' => $formattedOrders->count()]);

            $response = [
                'success' => true,
                'stats' => $stats,
                'chartData' => $chartData,
                'latestOrders' => $formattedOrders
            ];
            Log::info('Réponse préparée avec succès');

            return response()->json($response);

        } catch (\Exception $e) {
            Log::error('Erreur lors du chargement des statistiques', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors du chargement des statistiques.',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
