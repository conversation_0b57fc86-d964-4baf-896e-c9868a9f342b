<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    public function index(Request $request)
    {
        try {
            $payments = Payment::with(['order.user'])
                ->when($request->search, function($query, $search) {
                    return $query->where(function($q) use ($search) {
                        $q->where('reference', 'like', "%{$search}%")
                          ->orWhereHas('order.user', function($q) use ($search) {
                              $q->where('name', 'like', "%{$search}%")
                                ->orWhere('email', 'like', "%{$search}%");
                          });
                    });
                })
                ->latest()
                ->paginate(10);

            return view('accountant.payments.index', compact('payments'));
        } catch (\Exception $e) {
            Log::error('Erreur dans PaymentController@index: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement des paiements.');
        }
    }

    public function create()
    {
        try {
            // Récupérer les commandes qui peuvent être payées (impayées ou partiellement payées)
            $orders = Order::whereIn('status', ['pending', 'processing'])
                ->with('user')
                ->get();
                
            return view('accountant.payments.create', compact('orders'));
        } catch (\Exception $e) {
            Log::error('Erreur dans PaymentController@create: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement du formulaire de création de paiement.');
        }
    }

    public function show(Payment $payment)
    {
        try {
            $payment->load(['order.user', 'order.items.product']);
            return view('accountant.payments.show', compact('payment'));
        } catch (\Exception $e) {
            Log::error('Erreur dans PaymentController@show: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement du paiement.');
        }
    }
}
