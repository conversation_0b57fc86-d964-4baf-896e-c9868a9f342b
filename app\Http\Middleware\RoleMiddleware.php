<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RoleMiddleware
{
    public function handle(Request $request, Closure $next, $role)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        if (!Auth::user()->hasRole($role)) {
            Auth::logout();
            session()->flash('error', 'Vous n\'avez pas les permissions nécessaires pour accéder à cette page.');
            return redirect()->route('login');
        }

        return $next($request);
    }
}