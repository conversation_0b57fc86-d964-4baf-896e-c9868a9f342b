<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Constants\Currency;

class Payment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'reference', // Référence unique du paiement (RGMT + poste + rang)
        'sale_id',
        'payment_schedule_id', // Peut être null si paiement direct
        'cashier_id',
        'position', // Poste de l'agent qui effectue le paiement
        'amount',
        'payment_method', // 'cash', 'bank_transfer', 'check', 'mobile_money'
        'reference_number', // Numéro de référence pour les paiements par chèque, virement, etc.
        'receipt_number',
        'notes',
        'payment_date',
        'status' // 'pending', 'completed', 'cancelled'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_date' => 'datetime'
    ];

    // Relations
    public function sale(): BelongsTo
    {
        return $this->belongsTo(Sale::class);
    }

    public function paymentSchedule(): BelongsTo
    {
        return $this->belongsTo(PaymentSchedule::class);
    }

    public function cashier(): BelongsTo
    {
        return $this->belongsTo(User::class, 'cashier_id');
    }

    // Accessors
    public function getFormattedAmountAttribute(): string
    {
        return Currency::format($this->amount);
    }

    public function getFormattedPaymentMethodAttribute(): string
    {
        return match($this->payment_method) {
            'cash' => 'Espèces',
            'bank_transfer' => 'Virement bancaire',
            'check' => 'Chèque',
            default => ucfirst($this->payment_method)
        };
    }
}
