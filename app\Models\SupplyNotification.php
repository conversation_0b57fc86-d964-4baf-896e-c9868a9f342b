<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupplyNotification extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'supplies_notifications';

    protected $fillable = [
        'supply_id',
        'notes',
        'type',
        'is_read',
        'user_id'
    ];

    protected $casts = [
        'is_read' => 'boolean'
    ];

    public function supply(): BelongsTo
    {
        return $this->belongsTo(Supply::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
