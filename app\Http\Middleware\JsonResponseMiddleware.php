<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class JsonResponseMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        $request->headers->set('Accept', 'application/json');

        $response = $next($request);

        if (!$response instanceof Response) {
            $response = response()->json([
                'success' => false,
                'error' => 'Une erreur inattendue s\'est produite.'
            ], 500);
        }

        return $response;
    }
}
