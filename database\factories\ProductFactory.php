<?php

namespace Database\Factories;

use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductFactory extends Factory
{
    /**
     * Le modèle associé à la factory.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Définir l'état par défaut du modèle.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->word(),
            'description' => $this->faker->optional()->sentence(),
            'stock_quantity' => $this->faker->randomFloat(2, 0, 1000),
            'unit' => $this->faker->randomElement(['T', 'Kg', 'L']),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
