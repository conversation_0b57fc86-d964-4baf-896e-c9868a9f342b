# 🧪 Guide de Test des Exports - Tableau de Bord Comptable

## 📋 Étapes pour Tester les Exports

### 1. 🔐 Connexion
1. Ouvrir: http://127.0.0.1:8000/login
2. **Email**: `<EMAIL>`
3. **Mot de passe**: `password`
4. C<PERSON>r sur "Se connecter"

### 2. 🏠 Accès au Tableau de Bord
Après connexion, vous serez redirigé vers:
- **URL**: http://127.0.0.1:8000/accountant/dashboard-professional

### 3. 📊 Section "Rapports et Exports"
Dans le tableau de bord, cherchez la section **"Rapports et Exports"** qui contient:

#### 🔄 Exports Disponibles:
- **📈 Export Excel des Ventes** - Bouton vert
- **📄 Export PDF des Paiements** - Bouton bleu  
- **📋 Export PDF du Tableau de Bord** - Bouton orange
- **📊 Rapport Personnalisé** - Bouton violet

#### 📅 Rapports Prédéfinis:
- **📅 Rapport Hebdomadaire** - Bouton bleu clair
- **📆 Rapport Mensuel** - Bouton vert clair
- **📈 Rapport de Performance** - Bouton orange clair

### 4. 🧪 Tests à Effectuer

#### Test 1: Export Excel des Ventes
1. Cliquer sur "📈 Export Excel des Ventes"
2. **Résultat attendu**: Téléchargement d'un fichier `.xlsx` ou `.csv`
3. **Vérification**: Le fichier doit contenir les données des ventes

#### Test 2: Export PDF des Paiements  
1. Cliquer sur "📄 Export PDF des Paiements"
2. **Résultat attendu**: Téléchargement d'un fichier PDF ou réponse JSON
3. **Vérification**: Données des paiements formatées

#### Test 3: Export PDF du Tableau de Bord
1. Cliquer sur "📋 Export PDF du Tableau de Bord"  
2. **Résultat attendu**: Téléchargement d'un PDF ou réponse JSON
3. **Vérification**: Résumé complet du tableau de bord

#### Test 4: Rapports Prédéfinis
1. Cliquer sur chaque bouton de rapport (Hebdomadaire, Mensuel, Performance)
2. **Résultat attendu**: Affichage des données du rapport dans une modal
3. **Vérification**: Données cohérentes avec la période sélectionnée

### 5. 🔍 Diagnostic des Problèmes

#### Si "ça ne répond pas":

1. **Vérifier la Console du Navigateur** (F12):
   - Erreurs JavaScript?
   - Erreurs de réseau (404, 500)?
   - Problèmes CSRF?

2. **Vérifier le Serveur Laravel**:
   ```bash
   # Dans le terminal, vérifier que le serveur tourne
   php artisan serve --host=127.0.0.1 --port=8000
   ```

3. **Tester les Routes Directement**:
   - Ouvrir: http://127.0.0.1:8000/test-server
   - Doit retourner: `{"success": true, "message": "Serveur Laravel opérationnel"}`

4. **Vérifier les Logs Laravel**:
   ```bash
   tail -f storage/logs/laravel.log
   ```

### 6. 🛠️ Routes de Test Disponibles

#### Routes Sans Authentification (pour diagnostic):
- **GET** `/test-server` - Test de base du serveur
- **POST** `/test-export-simple` - Test d'export simple

#### Routes Avec Authentification Comptable:
- **GET** `/accountant/dashboard/test-exports` - Test des routes d'export
- **POST** `/accountant/dashboard/test-direct-export` - Test d'export direct

### 7. 📱 Interface de Test Simple
Fichier: `test_simple.html`
- Ouvrir dans le navigateur pour tester sans authentification
- Permet de vérifier la connectivité de base

### 8. 🚨 Problèmes Courants

#### "404 Not Found"
- Vérifier que le serveur Laravel tourne
- Vérifier l'URL (http://127.0.0.1:8000)

#### "419 Page Expired" (CSRF)
- Actualiser la page
- Vérifier que le token CSRF est inclus dans les requêtes

#### "403 Forbidden"
- Vérifier que vous êtes connecté en tant que comptable
- Vérifier les permissions du rôle

#### "500 Internal Server Error"  
- Vérifier les logs Laravel
- Vérifier la configuration de la base de données

### 9. ✅ Résultats Attendus

Quand tout fonctionne correctement:
- ✅ Les boutons d'export répondent immédiatement
- ✅ Les fichiers se téléchargent ou les données s'affichent
- ✅ Aucune erreur dans la console du navigateur
- ✅ Les rapports prédéfinis s'affichent dans des modals

### 10. 📞 Support

Si les problèmes persistent:
1. Vérifier que toutes les dépendances sont installées
2. Vérifier la configuration de la base de données
3. Vérifier les permissions des fichiers
4. Consulter les logs détaillés

---

**🎯 Objectif**: Tous les exports et rapports doivent fonctionner sans erreur depuis le tableau de bord comptable professionnel.
