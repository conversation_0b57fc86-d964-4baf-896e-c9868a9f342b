<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Supply;

class CheckSupplies extends Command
{
    protected $signature = 'check:supplies';
    protected $description = 'Check supplies data';

    public function handle()
    {
        $supplies = Supply::with('supplier')->where('status', 'pending')->get();
        
        $this->info("Pending Supplies:");
        foreach ($supplies as $supply) {
            $this->line("ID: {$supply->id}");
            $this->line("Reference: {$supply->reference}");
            $this->line("Supplier ID: {$supply->supplier_id}");
            $this->line("Supplier: " . ($supply->supplier ? $supply->supplier->name : 'null'));
            $this->line("Status: {$supply->status}");
            $this->line("------------------------");
        }
    }
}
