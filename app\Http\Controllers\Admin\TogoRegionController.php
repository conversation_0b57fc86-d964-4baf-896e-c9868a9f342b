<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Region;
use Illuminate\Http\JsonResponse;

class TogoRegionController extends Controller
{
    /**
     * Récupère les régions du Togo avec leurs villes
     *
     * @return JsonResponse
     */
    public function getRegionsWithCities(): JsonResponse
    {
        try {
            $regions = Region::with(['cities' => function($query) {
                $query->select('id', 'name', 'region_id');
            }])
            ->select('id', 'name')
            ->get();

            if ($regions->isEmpty()) {
                return response()->json(['error' => 'Aucune région trouvée'], 404);
            }

            return response()->json($regions);
        } catch (\Exception $e) {
            \Log::error('Erreur dans getRegionsWithCities: ' . $e->getMessage());
            return response()->json(['error' => 'Une erreur est survenue lors de la récupération des régions'], 500);
        }
    }

    /**
     * Récupère les villes d'une région du Togo
     *
     * @param Region $region
     * @return JsonResponse
     */
    public function getCities(Region $region): JsonResponse
    {
        try {
            $cities = $region->cities()->select('id', 'name')->get();
            
            if ($cities->isEmpty()) {
                return response()->json(['error' => 'Aucune ville trouvée pour cette région'], 404);
            }

            return response()->json($cities);
        } catch (\Exception $e) {
            \Log::error('Erreur dans getCities: ' . $e->getMessage());
            return response()->json(['error' => 'Une erreur est survenue lors de la récupération des villes'], 500);
        }
    }
}
