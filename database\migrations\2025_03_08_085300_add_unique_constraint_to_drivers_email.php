<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Mettre à jour les emails vides ou en double
        DB::table('drivers')
            ->whereNull('email')
            ->orWhere('email', '')
            ->update([
                'email' => DB::raw('CONCAT(first_name, ".", last_name, "@example.com")')
            ]);

        Schema::table('drivers', function (Blueprint $table) {
            // Vérifier si la contrainte unique existe déjà
            $sm = Schema::getConnection()->getDoctrineSchemaManager();
            $indexes = $sm->listTableIndexes('drivers');
            
            if (!isset($indexes['drivers_email_unique'])) {
                $table->unique('email');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('drivers', function (Blueprint $table) {
            // Vérifier si la contrainte unique existe
            $sm = Schema::getConnection()->getDoctrineSchemaManager();
            $indexes = $sm->listTableIndexes('drivers');
            
            if (isset($indexes['drivers_email_unique'])) {
                $table->dropUnique(['email']);
            }
        });
    }
};
