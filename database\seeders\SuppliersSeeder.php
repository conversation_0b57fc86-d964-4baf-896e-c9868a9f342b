<?php

namespace Database\Seeders;

use App\Models\Supplier;
use Illuminate\Database\Seeder;

class SuppliersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $suppliers = [
            [
                'name' => 'CIMAF',
                'contact_person' => 'Responsable Commercial',
                'phone' => '+212 522 123456',
                'email' => '<EMAIL>',
                'address' => 'Zone Industrielle, Casablanca',
                'is_active' => true
            ],
            [
                'name' => 'LafargeHolcim Maroc',
                'contact_person' => 'Directeur Commercial',
                'phone' => '+212 522 654321',
                'email' => '<EMAIL>',
                'address' => 'Quartier industriel, Rabat',
                'is_active' => true
            ],
            [
                'name' => 'SOCOCIM',
                'contact_person' => 'Chef des Ventes',
                'phone' => '+221 338 339999',
                'email' => '<EMAIL>',
                'address' => 'Rufisque, Dakar, Sénégal',
                'is_active' => true
            ]
        ];

        foreach ($suppliers as $supplier) {
            Supplier::create($supplier);
        }
    }
}
