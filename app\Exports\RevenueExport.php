<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class RevenueExport implements FromCollection, WithHeadings, WithMapping, WithStyles
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        return collect([
            [
                'section' => 'Statistiques générales',
                'revenue' => $this->data['stats']['revenue'],
                'orders' => $this->data['stats']['orders'],
                'customers' => $this->data['stats']['customers'],
                'pending_orders' => $this->data['stats']['pending_orders']
            ],
            [
                'section' => 'Tendances',
                'revenue' => $this->data['stats']['revenue_trend'] . '%',
                'orders' => $this->data['stats']['orders_trend'] . '%',
                'customers' => $this->data['stats']['customers_trend'] . '%',
                'pending_orders' => $this->data['stats']['pending_orders_trend'] . '%'
            ],
            [
                'section' => 'Distribution des commandes',
                'completed' => $this->data['orderStatusData']['completed'],
                'pending' => $this->data['orderStatusData']['pending'],
                'cancelled' => $this->data['orderStatusData']['cancelled']
            ]
        ]);
    }

    public function headings(): array
    {
        return [
            'Section',
            'Revenus',
            'Commandes',
            'Clients',
            'En attente'
        ];
    }

    public function map($row): array
    {
        if (isset($row['completed'])) {
            return [
                $row['section'],
                $row['completed'],
                $row['pending'],
                $row['cancelled'],
                ''
            ];
        }

        return [
            $row['section'],
            $row['revenue'],
            $row['orders'],
            $row['customers'],
            $row['pending_orders']
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
            'A' => ['font' => ['bold' => true]],
        ];
    }
}
