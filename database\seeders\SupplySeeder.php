<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Supply;
use App\Models\SupplyDetail;
use App\Models\SupplyCity;
use App\Models\SupplyNotification;
use App\Models\Supplier;
use App\Models\User;
use App\Models\Category;
use App\Models\Product;
use App\Models\City;
use App\Models\Region;
use App\Models\Truck;
use App\Models\Driver;
use Carbon\Carbon;
use Illuminate\Support\Str;

class SupplySeeder extends Seeder
{
    private $referenceCounter = 1;

    public function run()
    {
        // Récupérer les données nécessaires
        $suppliers = Supplier::all();
        if ($suppliers->isEmpty()) {
            $this->command->error('Aucun fournisseur trouvé. Exécution de SuppliersSeeder...');
            $this->call(SuppliersSeeder::class);
            $suppliers = Supplier::all();
        }

        $users = User::all();
        if ($users->isEmpty()) {
            $this->command->error('Aucun utilisateur trouvé. Exécution de UserSeeder...');
            $this->call(UserSeeder::class);
            $users = User::all();
        }

        $categories = Category::all();
        if ($categories->isEmpty()) {
            $this->command->error('Aucune catégorie trouvée. Exécution de CategorySeeder...');
            $this->call(CategorySeeder::class);
            $categories = Category::all();
        }

        // Créer quelques produits si nécessaire
        $products = Product::all();
        if ($products->isEmpty()) {
            foreach ($categories as $category) {
                $name = 'Produit ' . $category->name;
                Product::create([
                    'name' => $name,
                    'slug' => Str::slug($name),
                    'category_id' => $category->id,
                    'code' => 'PROD-' . strtoupper(substr($category->name, 0, 3)) . '-001',
                    'price' => rand(1000, 5000),
                    'stock_quantity' => 0
                ]);
            }
            $products = Product::all();
        }

        $regions = Region::with('cities')->get();
        if ($regions->isEmpty()) {
            $this->command->error('Aucune région trouvée. Exécution de RegionsAndCitiesSeeder...');
            $this->call(RegionsAndCitiesSeeder::class);
            $regions = Region::with('cities')->get();
        }

        $trucks = Truck::all();
        if ($trucks->isEmpty()) {
            $this->command->error('Aucun camion trouvé. Exécution de TrucksSeeder...');
            $this->call(TrucksSeeder::class);
            $trucks = Truck::all();
        }

        $drivers = Driver::all();
        if ($drivers->isEmpty()) {
            $this->command->error('Aucun chauffeur trouvé. Exécution de DriversSeeder...');
            $this->call(DriversSeeder::class);
            $drivers = Driver::all();
        }

        // Créer les approvisionnements
        foreach ($suppliers as $supplier) {
            for ($i = 1; $i <= 3; $i++) {
                $total_amount = rand(50000, 200000);
                $total_remaining = $total_amount;
                $total_tonnage = rand(100, 500);
                $category = $categories->random();
                $created_by = $users->random()->id;
                
                // Créer l'approvisionnement avec une référence unique
                $supply = Supply::create([
                    'reference' => 'SUP' . date('Ymd') . str_pad($this->referenceCounter++, 4, '0', STR_PAD_LEFT),
                    'supplier_id' => $supplier->id,
                    'category_id' => $category->id,
                    'date' => Carbon::now(),
                    'expected_delivery_date' => Carbon::now()->addDays(rand(1, 7)),
                    'total_amount' => $total_amount,
                    'total_remaining' => $total_remaining,
                    'total_tonnage' => $total_tonnage,
                    'notes' => 'Commande d\'approvisionnement #' . $i,
                    'status' => 'pending',
                    'created_by' => $created_by
                ]);

                // Créer les détails
                $category_products = $products->where('category_id', $category->id);
                if ($category_products->isNotEmpty()) {
                    foreach ($regions->take(3) as $region) {
                        $product = $category_products->random();
                        $quantity = rand(10, 100);
                        $unit_price = rand(1000, 5000);
                        $total_price = $quantity * $unit_price;
                        $tonnage = $quantity;

                        SupplyDetail::create([
                            'supply_id' => $supply->id,
                            'product_id' => $product->id,
                            'region_id' => $region->id,
                            'quantity' => $quantity,
                            'unit_price' => $unit_price,
                            'total_price' => $total_price,
                            'tonnage' => $tonnage,
                            'status' => 'pending'
                        ]);

                        // Créer les villes pour chaque région
                        $region_cities = $region->cities;
                        if ($region_cities->isNotEmpty()) {
                            foreach ($region_cities->take(3) as $city) {
                                $truck = $trucks->random();
                                $driver = $drivers->random();
                                
                                SupplyCity::create([
                                    'supply_id' => $supply->id,
                                    'city_id' => $city->id,
                                    'vehicle_id' => $truck->id,
                                    'driver_id' => $driver->id,
                                    'quantity' => rand(5, 20),
                                    'price' => rand(500, 2000),
                                    'trips' => rand(1, 5)
                                ]);
                            }
                        }
                    }
                }

                // Créer une notification
                SupplyNotification::create([
                    'supply_id' => $supply->id,
                    'notes' => 'Nouvel approvisionnement créé',
                    'type' => 'creation',
                    'is_read' => false,
                    'user_id' => $created_by
                ]);
            }
        }
    }
}
