<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Tax;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TaxController extends Controller
{
    public function index()
    {
        try {
            $taxes = Tax::latest()->paginate(10);
            return view('accountant.taxes.index', compact('taxes'));
        } catch (\Exception $e) {
            Log::error('Erreur dans TaxController@index: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement des taxes.');
        }
    }

    public function create()
    {
        return view('accountant.taxes.create');
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'rate' => 'required|numeric|min:0|max:100',
                'description' => 'nullable|string|max:1000',
                'is_active' => 'boolean'
            ]);

            Tax::create($validated);

            return redirect()->route('accountant.taxes.index')
                ->with('success', 'La taxe a été créée avec succès.');
        } catch (\Exception $e) {
            Log::error('Erreur dans TaxController@store: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la création de la taxe.')
                ->withInput();
        }
    }

    public function edit(Tax $tax)
    {
        return view('accountant.taxes.edit', compact('tax'));
    }

    public function update(Request $request, Tax $tax)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'rate' => 'required|numeric|min:0|max:100',
                'description' => 'nullable|string|max:1000',
                'is_active' => 'boolean'
            ]);

            $tax->update($validated);

            return redirect()->route('accountant.taxes.index')
                ->with('success', 'La taxe a été mise à jour avec succès.');
        } catch (\Exception $e) {
            Log::error('Erreur dans TaxController@update: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la mise à jour de la taxe.')
                ->withInput();
        }
    }

    public function destroy(Tax $tax)
    {
        try {
            $tax->delete();
            return back()->with('success', 'La taxe a été supprimée avec succès.');
        } catch (\Exception $e) {
            Log::error('Erreur dans TaxController@destroy: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la suppression de la taxe.');
        }
    }
}
