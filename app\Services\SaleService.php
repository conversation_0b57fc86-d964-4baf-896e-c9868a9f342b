<?php

namespace App\Services;

use App\Models\Sale;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class SaleService
{
    public function generateInvoiceNumber(): string
    {
        $prefix = 'INV';
        $date = Carbon::now()->format('Ymd');
        $random = Str::upper(Str::random(4));
        return "{$prefix}-{$date}-{$random}";
    }

    public function generateQrCode(Sale $sale): string
    {
        $qrData = json_encode([
            'invoice' => $sale->invoice_number,
            'amount' => $sale->total_amount,
            'customer' => $sale->customer_name,
            'date' => $sale->created_at->format('Y-m-d H:i:s'),
            'product' => $sale->supply->details->first()->product->name,
            'quantity' => $sale->quantity,
            'verification_url' => route('sales.verify', $sale->id)
        ]);

        $qrCodePath = "qrcodes/sales/{$sale->invoice_number}.svg";
        QrCode::size(200)
            ->format('svg')
            ->style('round')
            ->eye('circle')
            ->color(44, 62, 80)
            ->errorCorrection('H')
            ->generate($qrData, public_path($qrCodePath));

        return $qrCodePath;
    }

    public function calculateDeliveryDate(Sale $sale): Carbon
    {
        // Logique pour calculer la date de livraison estimée
        $baseDeliveryTime = 24; // heures
        $additionalTimePerTrip = 2; // heures par voyage

        $totalDeliveryHours = $baseDeliveryTime + ($sale->trips * $additionalTimePerTrip);
        
        return Carbon::now()->addHours($totalDeliveryHours);
    }

    public function processPayment(Sale $sale, float $amount): bool
    {
        try {
            $sale->amount_paid += $amount;
            
            if ($sale->amount_paid >= $sale->total_amount) {
                $sale->payment_status = 'completed';
            } elseif ($sale->amount_paid > 0) {
                $sale->payment_status = 'partial';
            }

            $sale->save();
            return true;
        } catch (\Exception $e) {
            \Log::error('Erreur lors du traitement du paiement: ' . $e->getMessage());
            return false;
        }
    }

    public function updateDeliveryStatus(Sale $sale, string $status): bool
    {
        try {
            DB::beginTransaction();

            $sale->delivery_status = $status;
            if ($status === 'completed') {
                $sale->delivery_date = Carbon::now();
                
                // Mettre à jour la quantité restante dans supply_cities
                $supplyCity = $sale->supply->cities()
                    ->where('city_id', $sale->city_id)
                    ->first();

                if ($supplyCity) {
                    $supplyCity->remaining_quantity = max(0, $supplyCity->remaining_quantity - $sale->quantity);
                    $supplyCity->save();
                }
            }
            $sale->save();

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Erreur lors de la mise à jour du statut de livraison: ' . $e->getMessage());
            return false;
        }
    }
}
