<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\Category;
use App\Models\ProductPrice;
use App\Models\IronSpecification;
use App\Models\City;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Produits de ciment
        $cementCategory = Category::where('name', 'Ciment')->first();
        
        if ($cementCategory) {
            $cementProducts = [
                [
                    'name' => 'Ciment Portland CEM I 42.5',
                    'slug' => Str::slug('Ciment Portland CEM I 42.5'),
                    'price' => 85000,
                    'stock_quantity' => 1000,
                    'unit' => 'Tonne',
                    'is_active' => true,
                    'category_id' => $cementCategory->id,
                    'prices' => [
                        'Douala' => 85000,
                        'Yaoundé' => 87000,
                        'Bafoussam' => 89000,
                        'Garoua' => 92000,
                    ]
                ],
                [
                    'name' => 'Ciment Portland CEM II 32.5',
                    'slug' => Str::slug('Ciment Portland CEM II 32.5'),
                    'price' => 82000,
                    'stock_quantity' => 800,
                    'unit' => 'Tonne',
                    'is_active' => true,
                    'category_id' => $cementCategory->id,
                    'prices' => [
                        'Douala' => 82000,
                        'Yaoundé' => 84000,
                        'Bafoussam' => 86000,
                        'Garoua' => 89000,
                    ]
                ],
                [
                    'name' => 'Ciment Blanc',
                    'slug' => Str::slug('Ciment Blanc'),
                    'price' => 95000,
                    'stock_quantity' => 200,
                    'unit' => 'Tonne',
                    'is_active' => true,
                    'category_id' => $cementCategory->id,
                    'prices' => [
                        'Douala' => 95000,
                        'Yaoundé' => 97000,
                        'Bafoussam' => 99000,
                        'Garoua' => 102000,
                    ]
                ],
                [
                    'name' => 'Ciment à Prise Rapide',
                    'slug' => Str::slug('Ciment à Prise Rapide'),
                    'price' => 100000,
                    'stock_quantity' => 150,
                    'unit' => 'Tonne',
                    'is_active' => false, // Produit temporairement inactif
                    'category_id' => $cementCategory->id,
                    'prices' => [
                        'Douala' => 100000,
                        'Yaoundé' => 102000,
                        'Bafoussam' => 104000,
                        'Garoua' => 107000,
                    ]
                ],
            ];

            // Créer les produits de ciment avec leurs prix par ville
            foreach ($cementProducts as $product) {
                $prices = $product['prices'];
                unset($product['prices']);
                
                $newProduct = Product::create($product);

                // Ajouter les prix par ville
                foreach ($prices as $cityName => $price) {
                    $city = City::where('name', $cityName)->first();
                    if ($city) {
                        ProductPrice::create([
                            'product_id' => $newProduct->id,
                            'city_id' => $city->id,
                            'price' => $price
                        ]);
                    }
                }
            }
        }

        // Produits de fer
        $ironCategory = Category::where('name', 'Fer')->first();
        
        if ($ironCategory) {
            $ironProducts = [
                [
                    'name' => 'Fer à Béton 6mm',
                    'slug' => Str::slug('Fer à Béton 6mm'),
                    'price' => 750000,
                    'stock_quantity' => 1000,
                    'unit' => 'Barre',
                    'is_active' => true,
                    'category_id' => $ironCategory->id,
                    'specifications' => [
                        'diameter' => 6,
                        'length' => 12,
                        'units_per_ton' => 750,
                        'weight_per_unit' => 1.33,
                        'unit_price' => 1000,
                        'ton_price' => 750000
                    ]
                ],
                [
                    'name' => 'Fer à Béton 8mm',
                    'slug' => Str::slug('Fer à Béton 8mm'),
                    'price' => 422000,
                    'stock_quantity' => 800,
                    'unit' => 'Barre',
                    'is_active' => true,
                    'category_id' => $ironCategory->id,
                    'specifications' => [
                        'diameter' => 8,
                        'length' => 12,
                        'units_per_ton' => 422,
                        'weight_per_unit' => 2.37,
                        'unit_price' => 1780,
                        'ton_price' => 750000
                    ]
                ],
                [
                    'name' => 'Fer à Béton 10mm',
                    'slug' => Str::slug('Fer à Béton 10mm'),
                    'price' => 270000,
                    'stock_quantity' => 600,
                    'unit' => 'Barre',
                    'is_active' => true,
                    'category_id' => $ironCategory->id,
                    'specifications' => [
                        'diameter' => 10,
                        'length' => 12,
                        'units_per_ton' => 270,
                        'weight_per_unit' => 3.70,
                        'unit_price' => 2780,
                        'ton_price' => 750000
                    ]
                ],
                [
                    'name' => 'Fer à Béton 12mm',
                    'slug' => Str::slug('Fer à Béton 12mm'),
                    'price' => 188000,
                    'stock_quantity' => 400,
                    'unit' => 'Barre',
                    'is_active' => true,
                    'category_id' => $ironCategory->id,
                    'specifications' => [
                        'diameter' => 12,
                        'length' => 12,
                        'units_per_ton' => 188,
                        'weight_per_unit' => 5.32,
                        'unit_price' => 3990,
                        'ton_price' => 750000
                    ]
                ],
                [
                    'name' => 'Fer à Béton 14mm',
                    'slug' => Str::slug('Fer à Béton 14mm'),
                    'price' => 138000,
                    'stock_quantity' => 300,
                    'unit' => 'Barre',
                    'is_active' => false, // Temporairement en rupture de stock
                    'category_id' => $ironCategory->id,
                    'specifications' => [
                        'diameter' => 14,
                        'length' => 12,
                        'units_per_ton' => 138,
                        'weight_per_unit' => 7.25,
                        'unit_price' => 5435,
                        'ton_price' => 750000
                    ]
                ],
                [
                    'name' => 'Fer à Béton 16mm',
                    'slug' => Str::slug('Fer à Béton 16mm'),
                    'price' => 106000,
                    'stock_quantity' => 200,
                    'unit' => 'Barre',
                    'is_active' => true,
                    'category_id' => $ironCategory->id,
                    'specifications' => [
                        'diameter' => 16,
                        'length' => 12,
                        'units_per_ton' => 106,
                        'weight_per_unit' => 9.43,
                        'unit_price' => 7075,
                        'ton_price' => 750000
                    ]
                ],
            ];

            // Créer les produits de fer avec leurs spécifications
            foreach ($ironProducts as $product) {
                $specifications = $product['specifications'];
                unset($product['specifications']);
                
                $newProduct = Product::create($product);

                // Ajouter les spécifications
                IronSpecification::create(array_merge(
                    ['product_id' => $newProduct->id],
                    $specifications
                ));
            }
        }
    }
}
