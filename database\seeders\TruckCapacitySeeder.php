<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\TruckCapacity;

class TruckCapacitySeeder extends Seeder
{
    public function run()
    {
        $capacities = [
            [
                'name' => 'Camion 20T',
                'capacity' => 20,
                'unit' => 'tonnes',
                'description' => 'Camion 20 tonnes',
                'is_active' => true
            ],
            [
                'name' => 'Camion 25T',
                'capacity' => 25,
                'unit' => 'tonnes',
                'description' => 'Camion 25 tonnes',
                'is_active' => true
            ],
            [
                'name' => 'Camion 30T',
                'capacity' => 30,
                'unit' => 'tonnes',
                'description' => 'Camion 30 tonnes',
                'is_active' => true
            ],
            [
                'name' => 'Camion 35T',
                'capacity' => 35,
                'unit' => 'tonnes',
                'description' => 'Camion 35 tonnes',
                'is_active' => true
            ],
            [
                'name' => 'Camion 40T',
                'capacity' => 40,
                'unit' => 'tonnes',
                'description' => 'Camion 40 tonnes',
                'is_active' => true
            ],
            [
                'name' => 'Camion 45T',
                'capacity' => 45,
                'unit' => 'tonnes',
                'description' => 'Camion 45 tonnes',
                'is_active' => true
            ],
            [
                'name' => 'Camion 50T',
                'capacity' => 50,
                'unit' => 'tonnes',
                'description' => 'Camion 50 tonnes',
                'is_active' => true
            ],
            [
                'name' => 'Camion 55T',
                'capacity' => 55,
                'unit' => 'tonnes',
                'description' => 'Camion 55 tonnes',
                'is_active' => true
            ],
        ];

        foreach ($capacities as $capacity) {
            TruckCapacity::firstOrCreate(
                ['name' => $capacity['name']],
                $capacity
            );
        }
    }
}
