<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_histories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->foreignId('supply_id')->nullable()->constrained('supplies')->onDelete('set null');
            $table->string('type')->comment('Type de mouvement (supply, adjustment, etc.)');
            $table->decimal('quantity', 10, 2)->comment('Quantité ajoutée ou retirée');
            $table->decimal('previous_stock', 10, 2)->comment('Stock avant le mouvement');
            $table->decimal('new_stock', 10, 2)->comment('Stock après le mouvement');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->text('notes')->nullable()->comment('Notes ou commentaires sur le mouvement');
            $table->timestamps();

            // Index pour améliorer les performances des requêtes
            $table->index(['product_id', 'created_at']);
            $table->index(['supply_id', 'created_at']);
            $table->index(['type', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_histories');
    }
};
