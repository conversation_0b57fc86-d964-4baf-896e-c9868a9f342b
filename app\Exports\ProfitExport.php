<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class ProfitExport implements FromCollection, WithHeadings, WithStyles, WithTitle
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        $collection = collect();
        
        // Résumé
        $collection->push([
            'Indicateur', 'Montant (FCFA)', 'Pourcentage (%)', '', '', ''
        ]);
        $collection->push([
            'Revenus', number_format($this->data['revenue'], 0, ',', ' '), '100.00', '', '', ''
        ]);
        $collection->push([
            'Coûts', number_format($this->data['costs'], 0, ',', ' '), 
            $this->data['revenue'] > 0 ? number_format(($this->data['costs'] / $this->data['revenue']) * 100, 2) : '0.00',
            '', '', ''
        ]);
        $collection->push([
            'BÉNÉFICE NET', number_format($this->data['profit'], 0, ',', ' '), 
            number_format($this->data['profit_margin'], 2), '', '', ''
        ]);
        $collection->push(['', '', '', '', '', '']); // Ligne vide

        // Évolution mensuelle
        $collection->push([
            'ÉVOLUTION MENSUELLE DES BÉNÉFICES', '', '', '', '', ''
        ]);
        $collection->push([
            'Mois', 'Année', 'Revenus (FCFA)', 'Coûts (FCFA)', 'Bénéfice (FCFA)', 'Marge (%)'
        ]);

        foreach ($this->data['monthly_profit'] as $monthly) {
            $monthlyMargin = $monthly->revenue > 0 ? ($monthly->profit / $monthly->revenue) * 100 : 0;
            $collection->push([
                \Carbon\Carbon::createFromDate($monthly->year, $monthly->month, 1)->format('F'),
                $monthly->year,
                number_format($monthly->revenue, 0, ',', ' '),
                number_format($monthly->costs, 0, ',', ' '),
                number_format($monthly->profit, 0, ',', ' '),
                number_format($monthlyMargin, 2)
            ]);
        }

        $collection->push(['', '', '', '', '', '']); // Ligne vide

        // Analyse
        $collection->push([
            'ANALYSE DES PERFORMANCES', '', '', '', '', ''
        ]);
        
        if ($this->data['profit'] > 0) {
            $collection->push([
                '✓ Bénéfice positif sur la période', '', '', '', '', ''
            ]);
        } else {
            $collection->push([
                '⚠ Bénéfice négatif ou nul', '', '', '', '', ''
            ]);
        }

        if ($this->data['profit_margin'] > 10) {
            $collection->push([
                '✓ Marge bénéficiaire satisfaisante (' . number_format($this->data['profit_margin'], 1) . '%)', '', '', '', '', ''
            ]);
        } else {
            $collection->push([
                '⚠ Marge bénéficiaire faible (' . number_format($this->data['profit_margin'], 1) . '%)', '', '', '', '', ''
            ]);
        }

        if ($this->data['revenue'] > $this->data['costs']) {
            $collection->push([
                '✓ Revenus supérieurs aux coûts', '', '', '', '', ''
            ]);
        } else {
            $collection->push([
                '⚠ Coûts supérieurs ou égaux aux revenus', '', '', '', '', ''
            ]);
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'RAPPORT DES BÉNÉFICES',
            'Période: ' . \Carbon\Carbon::parse($this->data['start_date'])->format('d/m/Y') . ' - ' . \Carbon\Carbon::parse($this->data['end_date'])->format('d/m/Y'),
            '',
            '',
            '',
            ''
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 16,
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => 'FF1CC88A',
                    ],
                ],
            ],
            2 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],
        ];
    }

    public function title(): string
    {
        return 'Bénéfices';
    }
}
