<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use App\Models\Supply;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        // Statistiques
        $stats = [
            'total_sales' => Order::whereMonth('created_at', Carbon::now()->month)
                ->where('status', 'completed')
                ->sum('total_amount'),
            'total_orders' => Order::count(),
            'active_customers' => User::role('customer')->where('is_active', true)->count(),
            'low_stock_products' => Product::where('stock_quantity', '<=', 10)->count(),
            'total_supplies' => Supply::where('created_by', auth()->id())->count(),
            'pending_supplies' => Supply::where('created_by', auth()->id())
                ->where('status', 'pending')
                ->count(),
            'total_supply_amount' => Supply::where('created_by', auth()->id())
                ->sum('total_amount'),
            'monthly_supplies' => Supply::where('created_by', auth()->id())
                ->whereMonth('created_at', Carbon::now()->month)
                ->count()
        ];

        // Dernières commandes
        $latestOrders = Order::with('user')
            ->latest()
            ->take(10)
            ->get();

        // Derniers approvisionnements
        $latestSupplies = Supply::with(['supplier', 'items.product'])
            ->where('created_by', auth()->id())
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Données pour le graphique des ventes
        $salesData = Order::where('status', 'completed')
            ->whereBetween('created_at', [Carbon::now()->subDays(6), Carbon::now()])
            ->selectRaw('DATE(created_at) as date, SUM(total_amount) as total')
            ->groupBy('date')
            ->get();

        // Données pour le graphique des approvisionnements par jour
        $dailySupplies = Supply::where('created_by', auth()->id())
            ->whereBetween('created_at', [Carbon::now()->subDays(6), Carbon::now()])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy(DB::raw('DATE(created_at)'))
            ->orderBy('date')
            ->get();

        // Données pour le graphique des catégories
        $categoryData = Order::with('orderItems.product.category')
            ->where('status', 'completed')
            ->whereMonth('created_at', Carbon::now()->month)
            ->get()
            ->flatMap(function ($order) {
                return $order->orderItems->map(function ($item) {
                    return [
                        'category' => $item->product->category->name,
                        'amount' => $item->total_price
                    ];
                });
            })
            ->groupBy('category')
            ->map(function ($items) {
                return $items->sum('amount');
            });

        // Données pour le graphique des montants par produit
        $amountByProduct = DB::table('supplies')
            ->join('supply_details', 'supplies.id', '=', 'supply_details.supply_id')
            ->join('products', 'products.id', '=', 'supply_details.product_id')
            ->where('supplies.created_by', auth()->id())
            ->select('products.name', DB::raw('SUM(supply_details.quantity * supply_details.unit_price) as total'))
            ->groupBy('products.id', 'products.name')
            ->orderBy('total', 'desc')
            ->limit(5)
            ->get();

        return view('accountant.dashboard', compact(
            'stats',
            'latestOrders',
            'latestSupplies',
            'salesData',
            'dailySupplies',
            'categoryData',
            'amountByProduct'
        ));
    }

    public function generateReport(Request $request)
    {
        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        $report = [
            'period' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d')
            ],
            'sales' => Order::whereBetween('created_at', [$startDate, $endDate])->sum('total_amount'),
            'orders' => Order::whereBetween('created_at', [$startDate, $endDate])->count(),
            'new_customers' => User::role('customer')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count(),
            'supplies' => Supply::whereBetween('created_at', [$startDate, $endDate])
                ->where('created_by', auth()->id())
                ->count(),
            'total_supply_amount' => Supply::whereBetween('created_at', [$startDate, $endDate])
                ->where('created_by', auth()->id())
                ->sum('total_amount'),
            'sales_by_category' => Product::select(
                'categories.name',
                DB::raw('SUM(order_items.quantity * products.price) as total_amount'),
                DB::raw('COUNT(DISTINCT orders.id) as order_count')
            )
                ->join('order_items', 'products.id', '=', 'order_items.product_id')
                ->join('orders', 'order_items.order_id', '=', 'orders.id')
                ->join('categories', 'products.category_id', '=', 'categories.id')
                ->whereBetween('orders.created_at', [$startDate, $endDate])
                ->groupBy('categories.id', 'categories.name')
                ->get()
        ];

        return response()->json($report);
    }
}
