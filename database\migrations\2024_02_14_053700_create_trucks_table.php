<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trucks', function (Blueprint $table) {
            $table->id();
            $table->string('registration_number')->unique();
            $table->string('brand');
            $table->string('model');
            $table->integer('year');
            $table->foreignId('supply_id')->nullable()->constrained()->nullOnDelete();
            $table->string('status')->default('available');
            $table->text('notes')->nullable();
            $table->foreignId('truck_capacity_id')->nullable()->constrained()->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('trucks');
    }
};
