<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('drivers', 'status')) {
            Schema::table('drivers', function (Blueprint $table) {
                $table->enum('status', ['available', 'unavailable'])->default('available');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('drivers', 'status')) {
            Schema::table('drivers', function (Blueprint $table) {
                $table->dropColumn('status');
            });
        }
    }
};
