<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sales', function (Blueprint $table) {
            if (!Schema::hasColumn('sales', 'price_modified')) {
                $table->boolean('price_modified')->default(false)->after('discount_total');
            }
            
            if (!Schema::hasColumn('sales', 'original_price')) {
                $table->decimal('original_price', 12, 2)->nullable()->after('price_modified');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sales', function (Blueprint $table) {
            $table->dropColumn('price_modified');
            $table->dropColumn('original_price');
        });
    }
};
