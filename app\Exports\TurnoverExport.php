<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class TurnoverExport implements FromCollection, WithHeadings, WithStyles, WithTitle
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        $collection = collect();
        
        // Résumé
        $collection->push([
            'Type', 'Montant (FCFA)', '', '', '', '', ''
        ]);
        $collection->push([
            'Chiffre d\'affaires des ventes', number_format($this->data['sales_turnover'], 0, ',', ' '), '', '', '', '', ''
        ]);
        $collection->push([
            'Chiffre d\'affaires des commandes', number_format($this->data['orders_turnover'], 0, ',', ' '), '', '', '', '', ''
        ]);
        $collection->push([
            'TOTAL', number_format($this->data['total_turnover'], 0, ',', ' '), '', '', '', '', ''
        ]);
        $collection->push(['', '', '', '', '', '', '']); // Ligne vide

        // Évolution mensuelle
        $collection->push([
            'ÉVOLUTION MENSUELLE', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Mois', 'Année', 'Nombre de ventes', 'Chiffre d\'affaires (FCFA)', 'Moyenne par vente (FCFA)', '', ''
        ]);

        foreach ($this->data['monthly_data'] as $monthly) {
            $collection->push([
                \Carbon\Carbon::createFromDate($monthly->year, $monthly->month, 1)->format('F'),
                $monthly->year,
                number_format($monthly->count),
                number_format($monthly->total, 0, ',', ' '),
                number_format($monthly->count > 0 ? $monthly->total / $monthly->count : 0, 0, ',', ' '),
                '',
                ''
            ]);
        }

        $collection->push(['', '', '', '', '', '', '']); // Ligne vide

        // Top produits
        $collection->push([
            'TOP PRODUITS', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Produit', 'Chiffre d\'affaires (FCFA)', 'Nombre de ventes', '', '', '', ''
        ]);

        foreach ($this->data['product_data'] as $product) {
            $collection->push([
                $product->product_name,
                number_format($product->total, 0, ',', ' '),
                number_format($product->count),
                '',
                '',
                '',
                ''
            ]);
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'RAPPORT DU CHIFFRE D\'AFFAIRES',
            'Période: ' . \Carbon\Carbon::parse($this->data['start_date'])->format('d/m/Y') . ' - ' . \Carbon\Carbon::parse($this->data['end_date'])->format('d/m/Y'),
            '',
            '',
            '',
            '',
            ''
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 16,
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => 'FF4472C4',
                    ],
                ],
            ],
            2 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],
        ];
    }

    public function title(): string
    {
        return 'Chiffre d\'affaires';
    }
}
