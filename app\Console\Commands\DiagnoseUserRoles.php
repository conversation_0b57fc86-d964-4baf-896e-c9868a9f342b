<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;

class DiagnoseUserRoles extends Command
{
    protected $signature = 'user:diagnose-roles {email}';
    protected $description = 'Diagnose user roles and permissions';

    public function handle()
    {
        $email = $this->argument('email');
        
        // Trouver l'utilisateur
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("Utilisateur avec l'email {$email} non trouvé.");
            return 1;
        }

        $this->info("\nInformations de l'utilisateur :");
        $this->info("--------------------------------");
        $this->info("ID : " . $user->id);
        $this->info("Nom : " . $user->name);
        $this->info("Email : " . $user->email);
        
        $this->info("\nR<PERSON><PERSON> directs :");
        $this->info("--------------------------------");
        foreach ($user->roles as $role) {
            $this->info("- " . $role->name . " (ID: " . $role->id . ")");
        }

        $this->info("\nRôles disponibles dans le système :");
        $this->info("--------------------------------");
        foreach (Role::all() as $role) {
            $this->info("- " . $role->name . " (ID: " . $role->id . ")");
            $this->info("  Utilisateurs avec ce rôle : " . $role->users()->count());
        }

        // Vérifier la table pivot role_user
        $this->info("\nEnregistrements dans role_user pour cet utilisateur :");
        $this->info("--------------------------------");
        $roleUsers = \DB::table('role_user')
            ->where('user_id', $user->id)
            ->get();
        
        foreach ($roleUsers as $roleUser) {
            $this->info("- Role ID: " . $roleUser->role_id . " | User ID: " . $roleUser->user_id);
        }

        return 0;
    }
}
