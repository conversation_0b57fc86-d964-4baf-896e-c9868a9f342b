<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Product;
use App\Models\CementOrder;
use App\Models\CementOrderNotification;
use Carbon\Carbon;

class CementOrderNotificationSeeder extends Seeder
{
    public function run()
    {
        // Créer d'abord quelques commandes de ciment
        $users = User::all();
        $products = Product::where('category_id', 1)->get(); // Supposant que 1 est l'ID de la catégorie ciment
        
        if ($users->isEmpty() || $products->isEmpty()) {
            return;
        }

        for ($i = 0; $i < 5; $i++) {
            $order = CementOrder::create([
                'reference' => 'CMD-' . date('Ymd') . '-' . str_pad($i + 1, 3, '0', STR_PAD_LEFT),
                'order_number' => 'ORD-' . str_pad($i + 1, 5, '0', STR_PAD_LEFT),
                'created_by' => $users->random()->id,
                'product_id' => $products->random()->id,
                'total_tonnage' => rand(100, 1000),
                'total_amount' => rand(1000000, 5000000),
                'total_price' => rand(1000000, 5000000),
                'status' => 'pending',
                'notes' => 'Note de test pour la commande ' . ($i + 1),
            ]);

            // Créer les notifications pour cette commande
            foreach ($users as $user) {
                CementOrderNotification::create([
                    'cement_order_id' => $order->id,
                    'user_id' => $user->id,
                    'type' => 'new_order',
                    'message' => "Nouvelle commande {$order->reference} créée pour un total de {$order->total_tonnage} tonnes.",
                    'status' => 'pending'
                ]);
            }
        }
    }
}
