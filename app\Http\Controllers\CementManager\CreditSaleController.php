<?php

namespace App\Http\Controllers\CementManager;

use App\Http\Controllers\Controller;
use App\Models\CementOrder;
use App\Models\CementOrderDetail;
use App\Models\TripAssignment;
use App\Models\CreditSale;
use App\Models\Stock;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CreditSaleController extends Controller
{
    public function index()
    {
        $creditSales = CreditSale::with([
            'cementOrderDetail.cementOrder',
            'cementOrderDetail.customer',
            'cementOrderDetail.product',
            'tripAssignment.driver',
            'tripAssignment.truck'
        ])->latest()->paginate(10);

        return view('cement-manager.credit-sales.index', compact('creditSales'));
    }

    public function confirmDelivery(Request $request, TripAssignment $assignment)
    {
        try {
            DB::beginTransaction();

            // Valider la requête
            $validated = $request->validate([
                'notes' => 'nullable|string'
            ]);

            $detail = $assignment->cementOrderDetail;

            // Vérifier que le détail n'a pas déjà été confirmé
            if ($detail->creditSales()->exists()) {
                throw new \Exception('Cette livraison a déjà été confirmée.');
            }

            // Créer la vente à crédit
            $creditSale = new CreditSale([
                'cement_order_id' => $detail->cement_order_id,
                'cement_order_detail_id' => $detail->id,
                'trip_assignment_id' => $assignment->id,
                'quantity' => $assignment->tonnage,
                'unit_price' => $detail->unit_price,
                'total_amount' => $assignment->tonnage * $detail->unit_price,
                'status' => 'pending_payment',
                'notes' => $validated['notes'] ?? null
            ]);

            $creditSale->save();

            // Mettre à jour le statut de l'affectation
            $assignment->update([
                'status' => 'completed',
                'completed_at' => now()
            ]);

            // Mettre à jour le stock
            $stock = Stock::firstOrCreate([
                'product_id' => $detail->product_id,
                'supplier_id' => $detail->supplier_id
            ]);

            $stock->quantity -= $assignment->tonnage;
            $stock->save();

            // Libérer le camion et le chauffeur
            $assignment->truck->update(['status' => 'available']);
            $assignment->driver->update(['status' => 'available']);

            // Mettre à jour les quantités dans le détail de la commande
            $detail->delivered_quantity = $detail->assignments->sum('tonnage');
            $detail->remaining_quantity = max(0, $detail->total_tonnage - $detail->delivered_quantity);
            $detail->save();

            DB::commit();

            return redirect()->route('cement-manager.dashboard')
                ->with('success', 'Livraison confirmée avec succès et stock mis à jour.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Une erreur est survenue : ' . $e->getMessage()]);
        }
    }

    public function show(CreditSale $creditSale)
    {
        $creditSale->load([
            'cementOrderDetail.cementOrder',
            'cementOrderDetail.customer',
            'cementOrderDetail.product',
            'tripAssignment.driver',
            'tripAssignment.truck'
        ]);

        return view('cement-manager.credit-sales.show', compact('creditSale'));
    }
}
