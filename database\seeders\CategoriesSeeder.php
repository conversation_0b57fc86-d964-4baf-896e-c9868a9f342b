<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;

class CategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Ciment',
                'slug' => 'ciment',
                'description' => 'Catégorie pour les produits de ciment',
                'is_active' => true,
                'type' => 'cement'
            ],
            [
                'name' => 'Fer',
                'slug' => 'fer',
                'description' => 'Catégorie pour les produits en fer',
                'is_active' => true,
                'type' => 'iron'
            ],
            [
                'name' => 'Matériaux de construction',
                'slug' => 'materiaux-construction',
                'description' => 'Catégorie pour les matériaux de construction divers',
                'is_active' => true,
                'type' => 'standard'
            ]
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
