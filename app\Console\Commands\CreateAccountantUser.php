<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Spatie\Permission\Models\Role;

class CreateAccountantUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:accountant';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Créer un utilisateur comptable de test';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            // Vérifier si l'utilisateur existe déjà
            $existingUser = User::where('email', '<EMAIL>')->first();

            if ($existingUser) {
                $this->info('✅ Utilisateur comptable existe déjà: <EMAIL>');
                $this->info('🔑 Mot de passe: password');
                $this->info('🌐 URL: http://127.0.0.1:8000/login');
                return 0;
            }

            // Créer l'utilisateur comptable
            $user = User::create([
                'name' => 'Comptable Test',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]);

            // Vérifier si le rôle accountant existe
            $role = Role::where('name', 'accountant')->first();
            if (!$role) {
                $role = Role::create(['name' => 'accountant']);
                $this->info('🔧 Rôle accountant créé');
            }

            // Assigner le rôle comptable
            $user->assignRole('accountant');

            $this->info('✅ Utilisateur comptable créé avec succès!');
            $this->info('📧 Email: <EMAIL>');
            $this->info('🔑 Mot de passe: password');
            $this->info('👤 Rôle: accountant');
            $this->info('🌐 URL de connexion: http://127.0.0.1:8000/login');
            $this->info('');
            $this->info('🚀 Vous pouvez maintenant vous connecter et tester les exports!');

            return 0;

        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de la création de l\'utilisateur: ' . $e->getMessage());
            return 1;
        }
    }
}
