<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\JsonResponse;

class ProductController extends Controller
{
    /**
     * Get products by category
     *
     * @param Category $category
     * @return JsonResponse
     */
    public function getProductsByCategory(Category $category): JsonResponse
    {
        $products = $category->products()
            ->select('id', 'name')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return response()->json($products);
    }
}
