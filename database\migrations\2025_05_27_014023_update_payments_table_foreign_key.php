<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            // Supprimer l'ancienne contrainte de clé étrangère
            $table->dropForeign(['credit_sale_id']);
            
            // Renommer la colonne pour qu'elle corresponde à la table sales
            $table->renameColumn('credit_sale_id', 'sale_id');
        });
        
        Schema::table('payments', function (Blueprint $table) {
            // Ajouter la nouvelle contrainte de clé étrangère vers la table sales
            $table->foreign('sale_id')->references('id')->on('sales');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            // Supprimer la contrainte de clé étrangère vers sales
            $table->dropForeign(['sale_id']);
            
            // Renommer la colonne pour revenir à l'ancien nom
            $table->renameColumn('sale_id', 'credit_sale_id');
        });
        
        Schema::table('payments', function (Blueprint $table) {
            // Rétablir la contrainte de clé étrangère vers credit_sales
            $table->foreign('credit_sale_id')->references('id')->on('credit_sales');
        });
    }
};
