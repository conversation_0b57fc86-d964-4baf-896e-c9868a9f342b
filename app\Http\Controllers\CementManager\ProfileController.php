<?php

namespace App\Http\Controllers\CementManager;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class ProfileController extends Controller
{
    public function show()
    {
        $user = Auth::user();
        return view('cement-manager.profile.show', compact('user'));
    }

    public function edit()
    {
        $user = Auth::user();
        return view('cement-manager.profile.edit', compact('user'));
    }

    public function update(Request $request)
    {
        $user = Auth::user();

        $validated = $request->validate([
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => ['nullable', 'string', 'max:20'],
            'avatar' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
        ]);

        // Gérer l'upload de l'avatar
        if ($request->hasFile('avatar') && $request->file('avatar')->isValid()) {
            try {
                // Supprimer l'ancien avatar s'il existe
                if ($user->avatar) {
                    $oldAvatarPath = public_path($user->avatar);
                    if (file_exists($oldAvatarPath)) {
                        @unlink($oldAvatarPath);
                    }
                }

                // Générer un nom de fichier unique
                $avatarName = time() . '_' . uniqid() . '.' . $request->file('avatar')->getClientOriginalExtension();
                
                // Créer le dossier s'il n'existe pas
                $uploadPath = public_path('uploads/avatars/cement-manager');
                if (!file_exists($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }
                
                // Déplacer le fichier vers le dossier uploads/avatars/cement-manager
                if ($request->file('avatar')->move($uploadPath, $avatarName)) {
                    // Enregistrer le chemin relatif dans la base de données
                    $user->avatar = 'uploads/avatars/cement-manager/' . $avatarName;
                } else {
                    // Log l'erreur si le déplacement a échoué
                    \Log::error('Erreur lors du déplacement de l\'avatar');
                    return redirect()->back()->with('error', 'Erreur lors de l\'upload de l\'avatar. Veuillez réessayer.');
                }
            } catch (\Exception $e) {
                // Log l'erreur pour débogage
                \Log::error('Erreur lors de l\'upload de l\'avatar: ' . $e->getMessage());
                return redirect()->back()->with('error', 'Erreur lors de l\'upload de l\'avatar: ' . $e->getMessage());
            }
        }

        // Combiner first_name et last_name pour la colonne name
        $user->name = $validated['first_name'] . ' ' . $validated['last_name'];
        $user->email = $validated['email'];
        $user->phone = $validated['phone'] ?? $user->phone;
        $user->save();

        return redirect()->route('cement-manager.profile.show')->with('success', 'Profil mis à jour avec succès');
    }

    public function showPasswordForm()
    {
        return view('cement-manager.profile.password');
    }

    public function updatePassword(Request $request)
    {
        $validated = $request->validate([
            'current_password' => ['required', 'string'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
        ]);

        $user = Auth::user();

        // Vérifier que le mot de passe actuel est correct
        if (!Hash::check($validated['current_password'], $user->password)) {
            return back()->withErrors(['current_password' => 'Le mot de passe actuel est incorrect']);
        }

        // Mettre à jour le mot de passe
        $user->password = Hash::make($validated['password']);
        $user->save();

        return redirect()->route('cement-manager.profile.show')->with('success', 'Mot de passe mis à jour avec succès');
    }
}
