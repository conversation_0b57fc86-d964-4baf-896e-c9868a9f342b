<?php

namespace App\Http\Controllers\CustomerService;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Sale;
use App\Models\User;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:customer_service']);
    }

    public function index()
    {
        $today = Carbon::today();

        $stats = [
            'total_customers' => User::role('customer')->count(),
            'active_customers' => User::role('customer')->where('is_active', true)->count(),
            'daily_sales' => Sale::whereDate('created_at', $today)->count(),
            'products_count' => Product::count(),
        ];

        $latest_sales = Sale::with(['user', 'products'])
            ->latest()
            ->take(5)
            ->get();

        $low_stock_products = Product::where('stock_quantity', '<', 10)
            ->latest()
            ->take(5)
            ->get();

        return view('customer-service.dashboard', compact('stats', 'latest_sales', 'low_stock_products'));
    }
}
