<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Region;
use Illuminate\Http\Request;

class AccountantRegionController extends Controller
{
    /**
     * Récupère les villes d'une région
     */
    public function getCities(Region $region)
    {
        try {
            \Log::info('Début de la récupération des villes pour la région', [
                'region_id' => $region->id,
                'region_name' => $region->name
            ]);

            // Vérifier si la région existe
            if (!$region->exists) {
                \Log::warning('Région non trouvée');
                return response()->json([
                    'success' => false,
                    'message' => 'Région non trouvée'
                ], 404);
            }

            // Récupérer les villes actives
            $cities = $region->cities()
                ->where('is_active', true)
                ->select('id', 'name')
                ->get();

            \Log::info('Villes récupérées avec succès', [
                'count' => $cities->count(),
                'cities' => $cities->toArray()
            ]);

            return response()->json([
                'success' => true,
                'cities' => $cities
            ]);

        } catch (\Exception $e) {
            \Log::error('Erreur lors de la récupération des villes', [
                'error' => $e->getMessage(),
                'region_id' => $region->id ?? null
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors de la récupération des villes'
            ], 500);
        }
    }
}
