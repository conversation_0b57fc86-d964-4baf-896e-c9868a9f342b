<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Supply;
use App\Models\Truck;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AdminSupplyController extends Controller
{
    public function index()
    {
        $supplies = Supply::with(['createdBy', 'supplier'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.supplies.index', compact('supplies'));
    }

    public function show(Supply $supply)
    {
        $supply->load([
            'createdBy',
            'validator',
            'details.product.category',
            'cities.city',
            'cities.vehicle',
            'cities.driver'
        ]);

        return view('admin.supplies.show', compact('supply'));
    }

    public function validateSupply(Supply $supply)
    {
        if ($supply->status !== 'pending') {
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cet approvisionnement ne peut plus être validé.'
                ], 400);
            }
            return redirect()->back()->with('error', 'Cet approvisionnement ne peut plus être validé.');
        }

        $supply->update([
            'status' => 'validated',
            'validator_id' => auth()->id(),
            'validated_at' => Carbon::now(),
        ]);

        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'L\'approvisionnement a été validé avec succès.',
                'redirect' => route('admin.supplies.index')
            ]);
        }

        return redirect()->route('admin.supplies.index')
            ->with('success', 'L\'approvisionnement a été validé avec succès.');
    }

    public function reject(Request $request, Supply $supply)
    {
        if ($supply->status !== 'pending') {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cet approvisionnement ne peut plus être rejeté.'
                ], 400);
            }
            return redirect()->back()->with('error', 'Cet approvisionnement ne peut plus être rejeté.');
        }

        $request->validate([
            'rejection_reason' => 'required|string|max:255'
        ]);

        $supply->update([
            'status' => 'rejected',
            'rejection_reason' => $request->rejection_reason,
            'validator_id' => auth()->id(),
            'validated_at' => Carbon::now(),
        ]);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'L\'approvisionnement a été rejeté avec succès.',
                'redirect' => route('admin.supplies.index')
            ]);
        }

        return redirect()->route('admin.supplies.index')
            ->with('success', 'L\'approvisionnement a été rejeté avec succès.');
    }
}
