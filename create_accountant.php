<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

// Bootstrap Laravel
$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

try {
    // Vérifier si l'utilisateur existe déjà
    $existingUser = \App\Models\User::where('email', '<EMAIL>')->first();
    
    if ($existingUser) {
        echo "✅ Utilisateur comptable existe déjà: <EMAIL>\n";
        echo "🔑 Mot de passe: password\n";
        echo "🌐 URL: http://127.0.0.1:8000/login\n";
        exit(0);
    }
    
    // Créer l'utilisateur comptable
    $user = \App\Models\User::create([
        'name' => 'Comptable Test',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'email_verified_at' => now(),
    ]);
    
    // Assigner le rôle comptable
    $user->assignRole('accountant');
    
    echo "✅ Utilisateur comptable créé avec succès!\n";
    echo "📧 Email: <EMAIL>\n";
    echo "🔑 Mot de passe: password\n";
    echo "👤 Rôle: accountant\n";
    echo "🌐 URL de connexion: http://127.0.0.1:8000/login\n";
    echo "\n";
    echo "🚀 Vous pouvez maintenant vous connecter et tester les exports!\n";
    
} catch (\Exception $e) {
    echo "❌ Erreur lors de la création de l'utilisateur: " . $e->getMessage() . "\n";
    echo "📝 Trace: " . $e->getTraceAsString() . "\n";
}
