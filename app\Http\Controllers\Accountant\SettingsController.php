<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class SettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:accountant']);
    }
    
    /**
     * Affiche la page des paramètres pour les comptables
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $user = Auth::user();
        $settings = Setting::all();
        
        try {
            Log::info('Chargement des paramètres pour l\'utilisateur: ' . $user->id);
            Log::info('Nombre de paramètres trouvés: ' . $settings->count());
        } catch (\Exception $e) {
            Log::error('Erreur lors du chargement des paramètres: ' . $e->getMessage());
        }
        
        return view('accountant.settings.index', compact('user', 'settings'));
    }
    
    /**
     * Met à jour le profil de l'utilisateur
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();
        
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                Rule::unique('users')->ignore($user->id),
            ],
            'phone' => 'nullable|string|max:20',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);
        
        $user->name = $request->name;
        $user->email = $request->email;
        $user->phone = $request->phone;
        
        if ($request->hasFile('avatar')) {
            // Traitement de l'upload de l'avatar
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $user->avatar = $avatarPath;
        }
        
        $user->save();
        
        return redirect()->route('accountant.settings.index')->with('success', 'Votre profil a été mis à jour avec succès.');
    }
    
    /**
     * Met à jour le mot de passe de l'utilisateur
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required|string',
            'password' => 'required|string|min:8|confirmed',
        ]);
        
        $user = Auth::user();
        
        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors(['current_password' => 'Le mot de passe actuel est incorrect.']);
        }
        
        $user->password = Hash::make($request->password);
        $user->save();
        
        return redirect()->route('accountant.settings.index')->with('success', 'Votre mot de passe a été mis à jour avec succès.');
    }
    
    /**
     * Met à jour les préférences de notification
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateNotifications(Request $request)
    {
        $user = Auth::user();
        
        // Dans un scénario réel, vous pourriez avoir une table de préférences de notification
        // Pour simplifier, nous stockons ces préférences dans un champ JSON
        $preferences = [
            'email_notifications' => $request->has('email_notifications'),
            'sale_alerts' => $request->has('sale_alerts'),
            'payment_alerts' => $request->has('payment_alerts'),
            'stock_alerts' => $request->has('stock_alerts'),
        ];
        
        $user->notification_preferences = json_encode($preferences);
        $user->save();
        
        return redirect()->route('accountant.settings.index')->with('success', 'Vos préférences de notification ont été mises à jour.');
    }
    
    /**
     * Met à jour les préférences d'interface utilisateur
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updatePreferences(Request $request)
    {
        $user = Auth::user();
        
        // Validation des préférences
        $request->validate([
            'theme' => 'nullable|string|in:default,light,dark,blue',
            'sidebar_collapsed' => 'nullable|boolean',
            'language' => 'nullable|string|in:fr,en',
            'date_format' => 'nullable|string|in:d/m/Y,Y-m-d,m/d/Y',
            'items_per_page' => 'nullable|integer|min:10|max:100',
        ]);
        
        // Récupérer les préférences existantes ou créer un tableau vide
        $preferences = $user->ui_preferences ? json_decode($user->ui_preferences, true) : [];
        
        // Mettre à jour les préférences avec les nouvelles valeurs
        $preferences['theme'] = $request->theme ?? ($preferences['theme'] ?? 'blue'); // Bleu par défaut selon préférence utilisateur
        $preferences['sidebar_collapsed'] = $request->has('sidebar_collapsed');
        $preferences['language'] = $request->language ?? ($preferences['language'] ?? 'fr');
        $preferences['date_format'] = $request->date_format ?? ($preferences['date_format'] ?? 'd/m/Y');
        $preferences['items_per_page'] = $request->items_per_page ?? ($preferences['items_per_page'] ?? 25);
        
        // Enregistrer les préférences mises à jour
        $user->ui_preferences = json_encode($preferences);
        $user->save();
        
        return redirect()->route('accountant.settings.index')->with('success', 'Vos préférences d\'interface ont été mises à jour avec succès.');
    }
}
