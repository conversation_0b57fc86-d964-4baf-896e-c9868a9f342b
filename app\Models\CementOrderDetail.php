<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Constants\Currency;

class CementOrderDetail extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'cement_order_id',
        'supplier_id',
        'destination_id',
        'tonnage_per_trip',
        'trips_count',
        'unit_price',
        'total_tonnage',
        'total_amount',
        'status',
        'remaining_quantity',
        'delivered_quantity'
    ];

    protected $casts = [
        'tonnage_per_trip' => 'float',
        'trips_count' => 'integer',
        'unit_price' => 'float',
        'total_tonnage' => 'float',
        'total_amount' => 'float',
        'remaining_quantity' => 'float',
        'delivered_quantity' => 'float'
    ];

    // Relations
    public function cementOrder(): BelongsTo
    {
        return $this->belongsTo(CementOrder::class);
    }

    public function destination(): BelongsTo
    {
        return $this->belongsTo(City::class, 'destination_id');
    }

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    public function tripAssignments(): HasMany
    {
        return $this->hasMany(TripAssignment::class);
    }

    public function assignments(): HasMany
    {
        return $this->hasMany(TripAssignment::class, 'cement_order_detail_id');
    }

    public function creditSales(): HasMany
    {
        return $this->hasMany(CreditSale::class);
    }

    // Accesseurs
    public function getNumberOfTripsAttribute(): int
    {
        return $this->trips_count ?? 0;
    }

    public function getTonnagePerTripFormattedAttribute(): string
    {
        return number_format($this->tonnage_per_trip, 2) . ' T';
    }

    public function getFormattedTotalTonnageAttribute()
    {
        return number_format($this->total_tonnage, 2) . ' T';
    }

    public function getFormattedRemainingQuantityAttribute()
    {
        return number_format($this->remaining_quantity, 2) . ' T';
    }

    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'pending' => 'En attente',
            'processing' => 'En cours',
            'completed' => 'Terminé',
            default => $this->status
        };
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'warning',
            'processing' => 'info',
            'completed' => 'success',
            default => 'secondary'
        };
    }

    // Accesseurs pour le formatage des montants
    public function getFormattedUnitPriceAttribute()
    {
        return number_format($this->unit_price, 0, ',', ' ') . ' FCFA';
    }

    public function getFormattedTotalAmountAttribute()
    {
        $total = $this->total_tonnage * $this->unit_price;
        return number_format($total, 0, ',', ' ') . ' FCFA';
    }

    public function getRemainingQuantityAttribute()
    {
        return $this->total_tonnage - ($this->delivered_quantity ?? 0);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeProcessing($query)
    {
        return $query->where('status', 'processing');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    // Mutators
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($detail) {
            $detail->total_tonnage = $detail->trips_count * $detail->tonnage_per_trip;
            $detail->total_amount = $detail->total_tonnage * $detail->unit_price;
            $detail->remaining_quantity = $detail->total_tonnage;
        });

        static::updating(function ($detail) {
            $detail->total_tonnage = $detail->trips_count * $detail->tonnage_per_trip;
            $detail->total_amount = $detail->total_tonnage * $detail->unit_price;
            
            // Ne mettre à jour remaining_quantity que si total_tonnage a changé
            if ($detail->isDirty('total_tonnage')) {
                $detail->remaining_quantity = $detail->total_tonnage - ($detail->delivered_quantity ?? 0);
            }

            // Mise à jour automatique du statut
            if ($detail->remaining_quantity <= 0) {
                $detail->status = 'completed';
            }
        });
    }
}
