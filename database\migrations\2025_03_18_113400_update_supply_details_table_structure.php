<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('supply_details', function (Blueprint $table) {
            // Drop columns that are not in the new specification
            $table->dropColumn([
                // 'description', // Keep this if it exists
                // 'rejection_reason', // Keep this if it exists
                // 'validated_at', // Keep this if it exists
                // 'validator_id' // Remove this line
            ]);

            // Add or modify columns according to new specification
            $table->foreignId('supply_id')->change();
            $table->foreignId('product_id')->change();
            $table->foreignId('region_id')->change();
            $table->decimal('quantity', 10, 2)->change();
            $table->decimal('unit_price', 10, 2)->change();
            $table->decimal('total_price', 10, 2)->change();
            $table->decimal('tonnage', 10, 2)->change();
            $table->string('status')->change();
            // Remove adding deleted_at since it already exists
            // $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::table('supply_details', function (Blueprint $table) {
            // Restore original columns
            $table->text('description')->nullable();
            $table->text('rejection_reason')->nullable();
            // $table->timestamp('validated_at')->nullable(); // Keep this if it exists
            // $table->foreignId('validator_id')->nullable(); // Comment this out

            // Drop new columns
            $table->dropColumn('deleted_at');
        });
    }
};