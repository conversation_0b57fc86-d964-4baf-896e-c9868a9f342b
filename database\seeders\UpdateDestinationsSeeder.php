<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Destination;
use App\Models\User;

class UpdateDestinationsSeeder extends Seeder
{
    public function run()
    {
        $user = User::first();

        if (!$user) {
            return;
        }

        $destinations = [
            [
                'name' => 'Kara',
                'type' => 'client',
                'is_active' => true,
                'created_by' => $user->id
            ],
            [
                'name' => 'Sokodé',
                'type' => 'client',
                'is_active' => true,
                'created_by' => $user->id
            ],
            [
                'name' => 'Atakpamé',
                'type' => 'client',
                'is_active' => true,
                'created_by' => $user->id
            ],
            [
                'name' => 'Tsévié',
                'type' => 'client',
                'is_active' => true,
                'created_by' => $user->id
            ]
        ];

        foreach ($destinations as $destination) {
            Destination::firstOrCreate(
                ['name' => $destination['name']],
                $destination
            );
        }
    }
}
