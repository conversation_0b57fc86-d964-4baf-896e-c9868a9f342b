<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Supprimer d'abord la contrainte enum existante
        DB::statement("ALTER TABLE supplies MODIFY COLUMN status VARCHAR(20) NOT NULL DEFAULT 'pending'");
        
        // Ajouter la nouvelle contrainte enum avec les valeurs mises à jour
        DB::statement("ALTER TABLE supplies MODIFY COLUMN status ENUM('pending', 'validated', 'rejected', 'partial', 'completed', 'cancelled') NOT NULL DEFAULT 'pending'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revenir à l'ancien enum
        DB::statement("ALTER TABLE supplies MODIFY COLUMN status ENUM('pending', 'partial', 'completed', 'cancelled') NOT NULL DEFAULT 'pending'");
    }
};
