<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Accountant\DriverController;
use App\Models\CementOrder;
use App\Models\CementOrderDetail;
use App\Models\Truck;
use App\Models\Driver;
use App\Models\TripAssignment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CementOrderDetailController extends Controller
{
    public function show(CementOrder $cement_order, CementOrderDetail $detail)
    {
        $detail->load([
            'customer', 
            'destination', 
            'supplier',
            'tripAssignments.truck',
            'tripAssignments.driver'
        ]);

        return view('accountant.cement-orders.details.show', compact('cement_order', 'detail'));
    }

    public function assignTruck(CementOrder $cement_order, CementOrderDetail $detail)
    {
        $trucks = Truck::with('capacity')->where('status', 'available')->get();
        $driverController = new DriverController();
        $drivers = $driverController->getAvailableDrivers();
        
        // Charger les relations nécessaires
        $cement_order->load('product');
        $detail->load(['customer', 'destination', 'supplier', 'tripAssignments', 'product']);
        
        // Formater les données des camions
        $trucks = $trucks->map(function ($truck) {
            $truck->formatted_name = $truck->registration_number . ' (' . $truck->capacity->tonnage . 'T)';
            return $truck;
        });

        // Formater la quantité totale
        $detail->formatted_total_tonnage = number_format($detail->total_tonnage, 2) . ' T';
        
        // Récupérer les numéros de voyages déjà affectés
        $assignedTrips = $detail->tripAssignments->pluck('trip_number')->toArray();
        
        return view('accountant.cement-orders.details.assign-truck', 
            compact('cement_order', 'detail', 'trucks', 'drivers', 'assignedTrips'));
    }

    public function storeTruckAssignment(Request $request, CementOrder $cement_order, CementOrderDetail $detail)
    {
        $validated = $request->validate([
            'truck_id' => 'required|exists:trucks,id',
            'driver_id' => 'required|exists:drivers,id',
            'planned_loading_date' => 'required|date|after:today',
            'planned_delivery_date' => 'required|date|after:planned_loading_date',
            'trip_number' => 'required|integer',
            'notes' => 'nullable|string'
        ]);

        // Vérifier la capacité du camion
        $truck = Truck::with('capacity')->find($validated['truck_id']);
        if ($truck->capacity->tonnage < $detail->tonnage_per_trip) {
            return back()->withErrors([
                'truck_id' => "La capacité du camion ({$truck->capacity->tonnage} T) est inférieure au tonnage requis par voyage ({$detail->tonnage_per_trip} T)"
            ])->withInput();
        }

        // Créer l'affectation du voyage
        $tripAssignment = new TripAssignment([
            'cement_order_id' => $cement_order->id,
            'cement_order_detail_id' => $detail->id,
            'truck_id' => $validated['truck_id'],
            'driver_id' => $validated['driver_id'],
            'trip_number' => $validated['trip_number'],
            'tonnage' => $detail->tonnage_per_trip,
            'status' => 'assigned',
            'assigned_at' => now(),
            'start_date' => $validated['planned_loading_date'],
            'end_date' => $validated['planned_delivery_date'],
            'notes' => $validated['notes']
        ]);
        $tripAssignment->save();

        // Mettre à jour le statut du camion
        $truck = Truck::find($validated['truck_id']);
        $truck->update(['status' => "busy"]);

        // Mettre à jour le statut du chauffeur
        $driver = Driver::find($validated['driver_id']);
        $driver->update(['status' => "busy"]);

        return redirect()
            ->route('accountant.cement-orders.show', $cement_order)
            ->with('success', 'Véhicule et chauffeur affectés avec succès');
    }
}
