<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Supply;
use App\Models\Product;
use App\Models\Region;

class SupplyItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'supply_id',
        'product_id',
        'region_id',
        'quantity',
        'tonnage',
        'unit_price',
        'total_price',
        'status'
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'tonnage' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2'
    ];

    protected $with = ['product.category'];

    protected $appends = ['formatted_quantity'];

    public function supply(): BelongsTo
    {
        return $this->belongsTo(Supply::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class);
    }

    /**
     * Vérifie si le produit est du ciment
     */
    public function isCementProduct(): bool
    {
        return $this->product && strtolower($this->product->category->name) === 'ciment';
    }

    /**
     * Vérifie si le produit est du fer
     */
    public function isIronProduct(): bool
    {
        return $this->product && strtolower($this->product->category->name) === 'fer';
    }

    /**
     * Obtient la quantité formatée en fonction du type de produit
     */
    public function getFormattedQuantityAttribute(): string
    {
        if ($this->isCementProduct()) {
            return number_format($this->tonnage, 2) . ' tonnes';
        }
        return number_format($this->quantity) . ' ' . ($this->product->unit ?? 'unités');
    }

    /**
     * Calcule le total en fonction du type de produit
     */
    public function calculateTotal()
    {
        if ($this->isCementProduct()) {
            $this->total_price = $this->tonnage * $this->unit_price;
        } else {
            $this->total_price = $this->quantity * $this->unit_price;
        }
        return $this->total_price;
    }

    /**
     * Met à jour le stock du produit
     */
    public function updateProductStock(): bool
    {
        if (!$this->product) {
            return false;
        }

        $currentStock = $this->product->stock_quantity;
        $addQuantity = $this->isCementProduct() ? $this->tonnage : $this->quantity;

        return $this->product->update([
            'stock_quantity' => $currentStock + $addQuantity
        ]);
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        // Avant la sauvegarde
        static::saving(function ($item) {
            $item->calculateTotal();
        });

        // Après la sauvegarde ou la mise à jour
        static::saved(function ($item) {
            if ($item->supply) {
                $item->supply->updateTotals();
            }
        });

        // Après la suppression
        static::deleted(function ($item) {
            if ($item->supply) {
                $item->supply->updateTotals();
            }
        });
    }
}
