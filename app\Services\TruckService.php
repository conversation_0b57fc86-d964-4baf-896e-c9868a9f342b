<?php

namespace App\Services;

use App\Models\Truck;
use App\Models\Driver;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;

class TruckService
{
    /**
     * Récupère les camions disponibles avec leurs chauffeurs
     */
    public function getAvailableTrucksWithDrivers()
    {
        \Log::info('Début de getAvailableTrucksWithDrivers');
        
        try {
            $trucks = Truck::select([
                'trucks.id',
                'trucks.registration_number',
                'trucks.status',
                'trucks.driver_id',
                'drivers.first_name',
                'drivers.last_name',
                'drivers.phone as driver_phone',
                'truck_capacities.capacity as capacity_value',
                'truck_capacities.unit as capacity_unit'
            ])
            ->leftJoin('drivers', function($join) {
                $join->on('trucks.driver_id', '=', 'drivers.id')
                     ->whereNull('drivers.deleted_at');
            })
            ->leftJoin('truck_capacities', 'trucks.truck_capacity_id', '=', 'truck_capacities.id')
            ->whereNull('trucks.deleted_at')
            ->where('trucks.status', 'assigned')
            ->where('trucks.driver_id', '!=', null)  // S'assurer que le camion a un chauffeur assigné
            ->get();

            \Log::info('Camions trouvés:', [
                'count' => $trucks->count(),
                'trucks' => $trucks->toArray()
            ]);

            return $trucks;
        } catch (\Exception $e) {
            \Log::error('Erreur lors de la récupération des camions:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Formate les camions pour le select
     */
    public function formatTrucksForSelect($trucks)
    {
        $formattedTrucks = $trucks->map(function ($truck) {
            // Format du texte d'affichage
            $displayText = sprintf(
                '%s - %s - %s%s',
                $truck->registration_number,
                ($truck->first_name && $truck->last_name) ? trim($truck->first_name . ' ' . $truck->last_name) : 'Non assigné',
                $truck->capacity_value,
                $truck->capacity_unit
            );

            // Formatage des informations du chauffeur
            return [
                'id' => $truck->id,
                'text' => $displayText,
                'registration_number' => $truck->registration_number,
                'first_name' => $truck->first_name,
                'last_name' => $truck->last_name,
                'driver_phone' => $truck->driver_phone,
                'capacity_value' => $truck->capacity_value,
                'capacity_unit' => $truck->capacity_unit,
                'status' => $truck->status
            ];
        })->toArray();

        \Log::info('Camions formatés:', ['trucks' => $formattedTrucks]);
        return $formattedTrucks;
    }
}
