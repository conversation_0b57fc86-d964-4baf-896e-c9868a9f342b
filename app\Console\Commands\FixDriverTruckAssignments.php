<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Driver;
use App\Models\Truck;
use Illuminate\Support\Facades\DB;

class FixDriverTruckAssignments extends Command
{
    protected $signature = 'drivers:fix-assignments';
    protected $description = 'Corriger les assignations entre chauffeurs et camions';

    public function handle()
    {
        $this->info('Début de la correction des assignations...');
        
        DB::transaction(function () {
            // Réinitialiser d'abord tous les driver_id des camions
            Truck::query()->update(['driver_id' => null, 'status' => 'available']);
            
            // Pour chaque chauffeur qui a un camion assigné
            $drivers = Driver::whereNotNull('truck_id')->get();
            
            foreach ($drivers as $driver) {
                $truck = Truck::find($driver->truck_id);
                if ($truck) {
                    $this->info("Assignation du camion {$truck->registration_number} au chauffeur {$driver->first_name} {$driver->last_name}");
                    
                    $truck->driver_id = $driver->id;
                    $truck->status = 'assigned';
                    $truck->save();
                }
            }
        });
        
        $this->info('Correction des assignations terminée !');
    }
}
