<?php

namespace App\Http\Controllers\Cashier;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Sale;
use App\Models\Category;
use App\Models\CementOrder;
use App\Models\CreditSale;
use App\Models\Payment;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:cashier']);
    }

    public function index()
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();

        // Récupérer les IDs des catégories pour le ciment et le fer
        $cementCategoryId = Category::where('slug', 'cement')->first()->id ?? null;
        $ironCategoryId = Category::where('slug', 'iron')->first()->id ?? null;

        // Statistiques des commandes de ciment
        $cementOrders = CementOrder::query();
        $pendingCementOrders = (clone $cementOrders)->where('status', 'pending')->count();
        $processingCementOrders = (clone $cementOrders)->where('status', 'processing')->count();

        // Statistiques des ventes à crédit
        $creditSales = CreditSale::query();
        $unpaidCreditSales = (clone $creditSales)->where('status', 'unpaid')->count();
        $partiallyPaidCreditSales = (clone $creditSales)->where('status', 'partially_paid')->count();

        // Statistiques des paiements
        $payments = Payment::query();
        $todayPayments = (clone $payments)->whereDate('payment_date', $today);
        $monthPayments = (clone $payments)->whereDate('payment_date', '>=', $thisMonth);

        $stats = [
            // Statistiques journalières
            'daily_sales_count' => Sale::whereDate('created_at', $today)->count(),
            'daily_sales_amount' => Sale::whereDate('created_at', $today)->sum('total_amount'),
            'daily_payments_count' => $todayPayments->count(),
            'daily_payments_amount' => $todayPayments->sum('amount'),
            
            // Statistiques mensuelles
            'monthly_payments_count' => $monthPayments->count(),
            'monthly_payments_amount' => $monthPayments->sum('amount'),
            
            // Statistiques des commandes de ciment
            'pending_cement_orders' => $pendingCementOrders,
            'processing_cement_orders' => $processingCementOrders,
            
            // Statistiques des ventes à crédit
            'unpaid_credit_sales' => $unpaidCreditSales,
            'partially_paid_credit_sales' => $partiallyPaidCreditSales,
            
            // Statistiques des produits
            'cement_products' => $cementCategoryId ? Product::where('category_id', $cementCategoryId)->count() : 0,
            'iron_products' => $ironCategoryId ? Product::where('category_id', $ironCategoryId)->count() : 0,
        ];

        // Dernières ventes à crédit
        $latest_credit_sales = CreditSale::with(['detail.customer', 'payments'])
            ->latest()
            ->take(5)
            ->get();

        // Derniers paiements
        $latest_payments = Payment::with(['creditSale.detail.customer', 'cashier'])
            ->latest('payment_date')
            ->take(5)
            ->get();

        // Dernières ventes
        $latest_sales = Sale::with('product')
            ->latest()
            ->take(5)
            ->get();

        // Produits disponibles
        $available_products = Product::where('stock_quantity', '>', 0)
            ->orderBy('category_id')
            ->orderBy('name')
            ->get();

        return view('cashier.dashboard', compact(
            'stats',
            'latest_sales',
            'latest_credit_sales',
            'latest_payments',
            'available_products'
        ));
    }
}
