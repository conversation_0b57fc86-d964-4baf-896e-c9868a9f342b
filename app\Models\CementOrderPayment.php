<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CementOrderPayment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'cement_order_id',
        'cement_order_detail_id',
        'trip_assignment_id',
        'cashier_id',
        'amount',
        'payment_type',
        'payment_method',
        'reference_number',
        'bank_name',
        'notes'
    ];

    protected $casts = [
        'amount' => 'decimal:2'
    ];

    // Relations
    public function cementOrder()
    {
        return $this->belongsTo(CementOrder::class);
    }

    public function cementOrderDetail()
    {
        return $this->belongsTo(CementOrderDetail::class);
    }

    public function tripAssignment()
    {
        return $this->belongsTo(Assignment::class, 'trip_assignment_id');
    }

    public function cashier()
    {
        return $this->belongsTo(User::class, 'cashier_id');
    }

    // Scopes
    public function scopeFullPayment($query)
    {
        return $query->where('payment_type', 'full');
    }

    public function scopePartialPayment($query)
    {
        return $query->where('payment_type', 'partial');
    }

    public function scopeCashPayment($query)
    {
        return $query->where('payment_method', 'cash');
    }

    public function scopeBankTransfer($query)
    {
        return $query->where('payment_method', 'bank_transfer');
    }

    public function scopeCheckPayment($query)
    {
        return $query->where('payment_method', 'check');
    }
}
