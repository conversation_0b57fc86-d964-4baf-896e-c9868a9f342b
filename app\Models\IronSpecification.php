<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IronSpecification extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'diameter',
        'length',
        'units_per_ton',
        'weight_per_unit',
        'unit_price',
        'ton_price'
    ];

    protected $casts = [
        'diameter' => 'float',
        'length' => 'float',
        'units_per_ton' => 'integer',
        'weight_per_unit' => 'float',
        'unit_price' => 'float',
        'ton_price' => 'float',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
