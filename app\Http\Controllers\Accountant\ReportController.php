<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\User;
use App\Models\Sale;
use App\Models\Supply;
use App\Models\Payment;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ReportController extends Controller
{
    public function sales(Request $request)
    {
        try {
            $period = $request->get('period', 'month');
            $startDate = $this->getStartDate($period);
            $endDate = $this->getEndDate($period);

            $sales = Order::where('status', 'completed')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('COUNT(*) as count'),
                    DB::raw('SUM(total) as total')
                )
                ->groupBy('date')
                ->get();

            return view('accountant.reports.sales', compact('sales', 'period'));
        } catch (\Exception $e) {
            Log::error('Erreur dans ReportController@sales: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la génération du rapport des ventes.');
        }
    }

    public function revenue(Request $request)
    {
        try {
            $period = $request->get('period', 'month');
            $startDate = $this->getStartDate($period);
            $endDate = $this->getEndDate($period);

            $revenue = Sale::where('status', 'completed')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('SUM(total_amount) as total'),
                    DB::raw('SUM(discount_total) as discount'),
                    DB::raw('SUM(total_before_discount) as total_before_discount')
                )
                ->groupBy('date')
                ->get();

            return view('accountant.reports.revenue', compact('revenue', 'period'));
        } catch (\Exception $e) {
            Log::error('Erreur dans ReportController@revenue: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la génération du rapport des revenus.');
        }
    }

    public function customers(Request $request)
    {
        try {
            $period = $request->get('period', 'month');
            $startDate = $this->getStartDate($period);
            $endDate = $this->getEndDate($period);

            $customers = User::whereHas('orders', function($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            })
            ->withCount(['orders' => function($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }])
            ->withSum(['orders' => function($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }], 'total_amount')
            ->orderByDesc('orders_sum_total_amount')
            ->paginate(10);

            return view('accountant.reports.customers', compact('customers', 'period'));
        } catch (\Exception $e) {
            Log::error('Erreur dans ReportController@customers: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la génération du rapport clients.');
        }
    }

    private function getStartDate($period)
    {
        switch($period) {
            case 'today':
                return Carbon::today();
            case 'week':
                return Carbon::now()->startOfWeek();
            case 'month':
                return Carbon::now()->startOfMonth();
            case 'year':
                return Carbon::now()->startOfYear();
            default:
                return Carbon::now()->startOfMonth();
        }
    }

    private function getEndDate($period)
    {
        switch($period) {
            case 'today':
                return Carbon::today()->endOfDay();
            case 'week':
                return Carbon::now()->endOfWeek();
            case 'month':
                return Carbon::now()->endOfMonth();
            case 'year':
                return Carbon::now()->endOfYear();
            default:
                return Carbon::now()->endOfMonth();
        }
    }
    
    /**
     * Affiche la page d'index des rapports
     */
    public function index()
    {
        try {
            // Statistiques générales pour le tableau de bord des rapports
            $totalSales = Sale::count();
            $totalRevenue = Sale::sum('total_amount');
            $totalPayments = Payment::sum('amount');
            $pendingPayments = max(0, $totalRevenue - $totalPayments);

            // Statistiques des factures par statut de paiement
            $paidInvoices = Sale::where('payment_status', 'paid')->count();
            $partialInvoices = Sale::where('payment_status', 'partial')->count();
            $unpaidInvoices = Sale::where('payment_status', 'unpaid')->count();

            // Calcul du taux de recouvrement
            $recoveryRate = $totalRevenue > 0 ? round(($totalPayments / $totalRevenue) * 100, 1) : 0;

            // Données pour les graphiques
            $monthlySales = $this->getMonthlySalesData();
            $paymentStats = $this->getPaymentStatusData();

            // Statistiques supplémentaires
            $totalCustomers = Sale::distinct('customer_name')->count('customer_name');
            $averageOrderValue = $totalSales > 0 ? round($totalRevenue / $totalSales, 0) : 0;

            return view('accountant.reports.index', compact(
                'totalSales',
                'totalRevenue',
                'totalPayments',
                'pendingPayments',
                'paidInvoices',
                'partialInvoices',
                'unpaidInvoices',
                'recoveryRate',
                'totalCustomers',
                'averageOrderValue',
                'monthlySales',
                'paymentStats'
            ));
        } catch (\Exception $e) {
            Log::error('Erreur dans ReportController@index: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement du tableau de bord des rapports.');
        }
    }
    
    /**
     * Génère un rapport sur les ventes
     */
    public function salesReport(Request $request)
    {
        try {
            $period = $request->get('period', 'month');
            $startDate = $this->getStartDate($period);
            $endDate = $this->getEndDate($period);

            $sales = Sale::whereBetween('created_at', [$startDate, $endDate])
                ->select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('COUNT(*) as count'),
                    DB::raw('SUM(total_amount) as total')
                )
                ->groupBy('date')
                ->get();

            return view('accountant.reports.sales', compact('sales', 'period'));
        } catch (\Exception $e) {
            Log::error('Erreur dans ReportController@salesReport: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la génération du rapport des ventes.');
        }
    }
    
    /**
     * Génère un rapport sur les approvisionnements
     */
    public function suppliesReport(Request $request)
    {
        try {
            $period = $request->get('period', 'month');
            $startDate = $this->getStartDate($period);
            $endDate = $this->getEndDate($period);

            $supplies = Supply::whereBetween('created_at', [$startDate, $endDate])
                ->select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('COUNT(*) as count'),
                    DB::raw('SUM(total_amount) as total'),
                    DB::raw('SUM(total_tonnage) as tonnage')
                )
                ->groupBy('date')
                ->get();

            return view('accountant.reports.supplies', compact('supplies', 'period'));
        } catch (\Exception $e) {
            Log::error('Erreur dans ReportController@suppliesReport: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la génération du rapport des approvisionnements.');
        }
    }
    
    /**
     * Génère un rapport sur les paiements
     */
    public function paymentsReport(Request $request)
    {
        try {
            $period = $request->get('period', 'month');
            $startDate = $this->getStartDate($period);
            $endDate = $this->getEndDate($period);

            $payments = Payment::whereBetween('created_at', [$startDate, $endDate])
                ->select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('COUNT(*) as count'),
                    DB::raw('SUM(amount) as total')
                )
                ->groupBy('date')
                ->get();

            return view('accountant.reports.payments', compact('payments', 'period'));
        } catch (\Exception $e) {
            Log::error('Erreur dans ReportController@paymentsReport: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la génération du rapport des paiements.');
        }
    }
    
    /**
     * Génère un rapport personnalisé
     */
    public function generateReport(Request $request)
    {
        try {
            $reportType = $request->get('type', 'sales');
            $startDate = $request->get('start_date') ? Carbon::parse($request->get('start_date')) : Carbon::now()->startOfMonth();
            $endDate = $request->get('end_date') ? Carbon::parse($request->get('end_date')) : Carbon::now();
            
            switch ($reportType) {
                case 'sales':
                    $data = Sale::whereBetween('created_at', [$startDate, $endDate])->get();
                    $view = 'accountant.reports.custom_sales';
                    break;
                case 'supplies':
                    $data = Supply::whereBetween('created_at', [$startDate, $endDate])->get();
                    $view = 'accountant.reports.custom_supplies';
                    break;
                case 'payments':
                    $data = Payment::whereBetween('created_at', [$startDate, $endDate])->get();
                    $view = 'accountant.reports.custom_payments';
                    break;
                default:
                    $data = [];
                    $view = 'accountant.reports.custom';
            }
            
            return view($view, compact('data', 'startDate', 'endDate', 'reportType'));
        } catch (\Exception $e) {
            Log::error('Erreur dans ReportController@generateReport: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la génération du rapport personnalisé.');
        }
    }

    /**
     * Récupère les données de ventes mensuelles pour les graphiques
     */
    private function getMonthlySalesData()
    {
        $startDate = Carbon::now()->startOfYear();
        $endDate = Carbon::now();

        // Récupérer les données réelles des ventes par mois
        $salesByMonth = Sale::whereBetween('created_at', [$startDate, $endDate])
            ->select(
                DB::raw('MONTH(created_at) as month'),
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(total_amount) as total')
            )
            ->groupBy('month')
            ->get()
            ->keyBy('month');

        // Créer un tableau complet pour tous les mois de l'année
        $monthNames = [
            1 => 'Jan', 2 => 'Fév', 3 => 'Mar', 4 => 'Avr',
            5 => 'Mai', 6 => 'Jun', 7 => 'Jul', 8 => 'Aoû',
            9 => 'Sep', 10 => 'Oct', 11 => 'Nov', 12 => 'Déc'
        ];

        $salesData = collect();
        for ($month = 1; $month <= 12; $month++) {
            $monthData = $salesByMonth->get($month);
            $salesData->push([
                'month' => $monthNames[$month],
                'count' => $monthData ? $monthData->count : 0,
                'total' => $monthData ? $monthData->total : 0
            ]);
        }

        return [
            'labels' => $salesData->pluck('month')->toArray(),
            'data' => $salesData->pluck('total')->toArray(),
            'counts' => $salesData->pluck('count')->toArray()
        ];
    }
    
    /**
     * Récupère les données sur le statut des paiements pour les graphiques
     */
    private function getPaymentStatusData()
    {
        // Récupérer les vraies données des statuts de paiement
        $paid = Sale::where('payment_status', 'paid')->count();
        $partial = Sale::where('payment_status', 'partial')->count();
        $unpaid = Sale::where('payment_status', 'unpaid')->count();

        return [
            'labels' => ['Payé', 'Partiel', 'Non payé'],
            'data' => [$paid, $partial, $unpaid],
            'colors' => ['#10b981', '#f59e0b', '#ef4444'],
            'paid' => $paid,
            'partial' => $partial,
            'unpaid' => $unpaid
        ];
    }
    
    /**
     * Affiche le formulaire de création d'un nouveau rapport
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        try {
            // Récupère les types de rapports disponibles
            $reportTypes = [
                'sales' => 'Rapport des ventes',
                'revenue' => 'Rapport des revenus',
                'customers' => 'Rapport des clients',
                'supplies' => 'Rapport des approvisionnements',
                'payments' => 'Rapport des paiements',
                'custom' => 'Rapport personnalisé'
            ];
            
            return view('accountant.reports.create', compact('reportTypes'));
        } catch (\Exception $e) {
            Log::error('Erreur dans ReportController@create: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement du formulaire de création de rapport.');
        }
    }
    
    /**
     * Enregistre un nouveau rapport
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'report_type' => 'required|string|in:sales,revenue,customers,supplies,payments,custom',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);
        
        try {
            DB::beginTransaction();
            
            // Ici, vous pourriez créer un enregistrement de rapport dans la base de données
            // Pour l'instant, nous allons simplement rediriger vers la méthode de génération appropriée
            
            $reportType = $request->report_type;
            $params = [
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'title' => $request->title,
                'description' => $request->description,
            ];
            
            DB::commit();
            
            // Rediriger vers le bon type de rapport
            switch ($reportType) {
                case 'sales':
                    return $this->salesReport($request);
                case 'revenue':
                    return $this->revenue($request);
                case 'customers':
                    return $this->customers($request);
                case 'supplies':
                    return $this->suppliesReport($request);
                case 'payments':
                    return $this->paymentsReport($request);
                case 'custom':
                    return $this->generateReport($request);
                default:
                    return redirect()->route('accountant.reports.index');
            }
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Erreur dans ReportController@store: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la création du rapport: ' . $e->getMessage());
        }
    }

    /**
     * Affiche les détails d'un rapport
     *
     * @param int $report Identifiant du rapport
     * @return \Illuminate\View\View
     */
    public function show($report)
    {
        try {
            // Pour l'instant, comme nous n'avons pas de modèle Report, nous allons simplement
            // générer un rapport à la volée basé sur le type demandé
            $reportType = request('type', 'sales');
            
            switch ($reportType) {
                case 'sales':
                    return $this->salesReport(request());
                case 'revenue':
                    return $this->revenue(request());
                case 'customers':
                    return $this->customers(request());
                case 'supplies':
                    return $this->suppliesReport(request());
                case 'payments':
                    return $this->paymentsReport(request());
                default:
                    return redirect()->route('accountant.reports.index');
            }
        } catch (\Exception $e) {
            Log::error('Erreur dans ReportController@show: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement du rapport.');
        }
    }
    
    /**
     * Édite un rapport existant
     *
     * @param int $report Identifiant du rapport
     * @return \Illuminate\View\View
     */
    public function edit($report)
    {
        try {
            // Comme nous n'avons pas de modèle Report pour l'instant, cette fonction
            // servira juste à rediriger vers le formulaire de création
            return redirect()->route('accountant.reports.create');
        } catch (\Exception $e) {
            Log::error('Erreur dans ReportController@edit: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement du rapport.');
        }
    }
    
    /**
     * Met à jour un rapport existant
     *
     * @param Request $request
     * @param int $report Identifiant du rapport
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $report)
    {
        try {
            // Comme nous n'avons pas de modèle Report pour l'instant, cette fonction est un placeholder
            return redirect()->route('accountant.reports.index');
        } catch (\Exception $e) {
            Log::error('Erreur dans ReportController@update: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la mise à jour du rapport.');
        }
    }
    
    /**
     * Supprime un rapport
     *
     * @param int $report Identifiant du rapport
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($report)
    {
        try {
            // Comme nous n'avons pas de modèle Report pour l'instant, cette fonction est un placeholder
            return redirect()->route('accountant.reports.index');
        } catch (\Exception $e) {
            Log::error('Erreur dans ReportController@destroy: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la suppression du rapport.');
        }
    }
    
    /**
     * Récupère les rapports par type
     *
     * @param string $type Type de rapport (sales, revenue, etc.)
     * @return \Illuminate\View\View
     */
    public function getByType($type)
    {
        try {
            switch ($type) {
                case 'sales':
                    return $this->salesReport(request());
                case 'revenue':
                    return $this->revenue(request());
                case 'customers':
                    return $this->customers(request());
                case 'supplies':
                    return $this->suppliesReport(request());
                case 'payments':
                    return $this->paymentsReport(request());
                default:
                    return redirect()->route('accountant.reports.index');
            }
        } catch (\Exception $e) {
            Log::error('Erreur dans ReportController@getByType: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement des rapports.');
        }
    }
    
    /**
     * Exporte un rapport au format CSV ou Excel
     *
     * @param int $report Identifiant du rapport
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export($report)
    {
        try {
            // Cette fonctionnalité nécessite l'installation de packages d'exportation comme Maatwebsite/Laravel-Excel
            // Pour l'instant, c'est juste un placeholder
            return back()->with('info', 'La fonctionnalité d\'exportation sera bientôt disponible.');
        } catch (\Exception $e) {
            Log::error('Erreur dans ReportController@export: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de l\'exportation du rapport.');
        }
    }
    
    /**
     * Génère un PDF du rapport et l'affiche
     *
     * @param int $report Identifiant du rapport
     * @return \Illuminate\Http\Response
     */
    public function print($report)
    {
        try {
            // Cette fonctionnalité nécessite l'installation du package DomPDF
            // Pour l'instant, c'est juste un placeholder
            return back()->with('info', 'La fonctionnalité d\'impression PDF sera bientôt disponible.');
        } catch (\Exception $e) {
            Log::error('Erreur dans ReportController@print: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de l\'impression du rapport.');
        }
    }
}
