<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Driver;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class AdminDriverController extends Controller
{
    public function index()
    {
        $drivers = Driver::latest()->get();
        return view('admin.drivers.index', compact('drivers'));
    }

    public function create()
    {
        return view('admin.drivers.create');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:drivers,email',
            'phone' => 'required|string|max:20',
            'license_number' => 'required|string|unique:drivers,license_number',
            'license_expiry' => 'required|date|after:today',
            'address' => 'nullable|string',
            'notes' => 'nullable|string',
            'status' => 'required|string|in:available,unavailable'
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        }

        DB::beginTransaction();
        try {
            Driver::create($request->all());

            DB::commit();
            return redirect()
                ->route('admin.drivers.index')
                ->with('success', 'Chauffeur créé avec succès');
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Erreur lors de la création du chauffeur: ' . $e->getMessage());
            return redirect()
                ->back()
                ->with('error', 'Une erreur est survenue lors de la création du chauffeur')
                ->withInput();
        }
    }

    public function edit(Driver $driver)
    {
        return view('admin.drivers.edit', compact('driver'));
    }

    public function update(Request $request, Driver $driver)
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:drivers,email,' . $driver->id,
            'phone' => 'required|string|max:20',
            'license_number' => 'required|string|unique:drivers,license_number,' . $driver->id,
            'license_expiry' => 'required|date|after:today',
            'address' => 'nullable|string',
            'notes' => 'nullable|string',
            'status' => 'required|string|in:available,unavailable'
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        }

        DB::beginTransaction();
        try {
            $driver->update($request->all());

            DB::commit();
            return redirect()
                ->route('admin.drivers.index')
                ->with('success', 'Chauffeur mis à jour avec succès');
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Erreur lors de la mise à jour du chauffeur: ' . $e->getMessage());
            return redirect()
                ->back()
                ->with('error', 'Une erreur est survenue lors de la mise à jour du chauffeur');
        }
    }

    public function destroy(Driver $driver)
    {
        try {
            $driver->delete();
            return redirect()
                ->route('admin.drivers.index')
                ->with('success', 'Chauffeur supprimé avec succès');
        } catch (\Exception $e) {
            Log::error('Erreur lors de la suppression du chauffeur: ' . $e->getMessage());
            return redirect()
                ->back()
                ->with('error', 'Une erreur est survenue lors de la suppression du chauffeur');
        }
    }
}
