<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\TruckService;
use Illuminate\Http\JsonResponse;

class TruckController extends Controller
{
    protected $truckService;

    public function __construct(TruckService $truckService)
    {
        $this->truckService = $truckService;
    }

    /**
     * Récupère les camions disponibles avec leurs chauffeurs
     */
    public function getAvailableTrucks(): JsonResponse
    {
        try {
            $trucks = $this->truckService->getAvailableTrucksWithDrivers();
            $formattedTrucks = $this->truckService->formatTrucksForSelect($trucks);

            return response()->json([
                'success' => true,
                'trucks' => $formattedTrucks
            ]);
        } catch (\Exception $e) {
            \Log::error('Erreur lors de la récupération des camions:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des camions',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
