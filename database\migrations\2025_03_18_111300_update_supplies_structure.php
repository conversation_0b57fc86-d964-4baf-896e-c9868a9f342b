<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('supplies', function (Blueprint $table) {
            // Drop any columns that are not in the new specification
            if (Schema::hasColumn('supplies', 'product_id')) {
                $table->dropForeign(['product_id']);
                $table->dropColumn('product_id');
            }

            // Add new columns only if they don't exist
            if (!Schema::hasColumn('supplies', 'rejection_reason')) {
                $table->text('rejection_reason')->nullable();
            }

            // Modify existing columns
            $table->text('notes')->nullable()->change();
            $table->decimal('total_tonnage', 10, 2)->default(0)->change();
            $table->decimal('total_amount', 12, 2)->default(0)->change();
            $table->decimal('total_remaining', 12, 2)->default(0)->change();
            $table->string('status')->default('pending')->change();

            // Add foreign key for validator_id if it doesn't exist
            if (!Schema::hasColumn('supplies', 'validator_id')) {
                $table->foreignId('validator_id')->nullable()->constrained('users')->nullOnDelete();
            }
            
            if (!Schema::hasColumn('supplies', 'validated_at')) {
                $table->timestamp('validated_at')->nullable();
            }
        });
    }

    public function down()
    {
        Schema::table('supplies', function (Blueprint $table) {
            // Add back product_id
            $table->foreignId('product_id')->nullable()->constrained('products')->nullOnDelete();
            
            // Drop columns added in up()
            if (Schema::hasColumn('supplies', 'rejection_reason')) {
                $table->dropColumn('rejection_reason');
            }
            if (Schema::hasColumn('supplies', 'validator_id')) {
                $table->dropForeign(['validator_id']);
                $table->dropColumn('validator_id');
            }
            if (Schema::hasColumn('supplies', 'validated_at')) {
                $table->dropColumn('validated_at');
            }
        });
    }
};
