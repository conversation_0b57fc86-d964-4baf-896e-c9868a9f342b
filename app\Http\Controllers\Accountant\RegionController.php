<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Region;
use Illuminate\Http\Request;

class RegionController extends Controller
{
    /**
     * Récupère les villes d'une région
     */
    public function getCities(Region $region)
    {
        try {
            \Log::info('Récupération des villes pour la région', [
                'region_id' => $region->id,
                'region_name' => $region->name
            ]);

            $cities = $region->cities()
                ->select('id', 'name')
                ->where('is_active', true)
                ->get();

            \Log::info('Villes récupérées avec succès', [
                'cities_count' => $cities->count(),
                'cities' => $cities->toArray()
            ]);

            return response()->json([
                'success' => true,
                'cities' => $cities
            ]);
        } catch (\Exception $e) {
            \Log::error('Erreur lors de la récupération des villes', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des villes: ' . $e->getMessage()
            ], 500);
        }
    }
}
