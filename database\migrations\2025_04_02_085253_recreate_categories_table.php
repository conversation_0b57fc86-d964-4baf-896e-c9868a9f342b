<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Supprimer d'abord les clés étrangères qui référencent la table categories
        Schema::table('products', function (Blueprint $table) {
            $table->dropForeign(['category_id']);
        });

        Schema::table('supplies', function (Blueprint $table) {
            $table->dropForeign(['category_id']);
        });

        // Recréer la table categories
        Schema::dropIfExists('categories');
        
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->string('type')->default('standard');
            $table->timestamps();
            $table->softDeletes();
        });

        // Recréer les clés étrangères
        Schema::table('products', function (Blueprint $table) {
            $table->foreign('category_id')->references('id')->on('categories')->onDelete('cascade');
        });

        Schema::table('supplies', function (Blueprint $table) {
            $table->foreign('category_id')->references('id')->on('categories')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Supprimer d'abord les clés étrangères
        Schema::table('products', function (Blueprint $table) {
            $table->dropForeign(['category_id']);
        });

        Schema::table('supplies', function (Blueprint $table) {
            $table->dropForeign(['category_id']);
        });

        // Supprimer la table
        Schema::dropIfExists('categories');

        // Recréer les clés étrangères avec l'ancienne structure
        Schema::table('products', function (Blueprint $table) {
            $table->foreign('category_id')->references('id')->on('categories');
        });

        Schema::table('supplies', function (Blueprint $table) {
            $table->foreign('category_id')->references('id')->on('categories');
        });
    }
};
