<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Truck;
use App\Models\TruckCapacity;
use Illuminate\Support\Facades\DB;

class FixTruckCapacities extends Command
{
    protected $signature = 'trucks:fix-capacities';
    protected $description = 'Assigner une capacité par défaut aux camions qui n\'en ont pas';

    public function handle()
    {
        $this->info('Début de la correction des capacités des camions...');

        // Récupérer la capacité par défaut (30 tonnes)
        $defaultCapacity = TruckCapacity::where('capacity', 30)->first();

        if (!$defaultCapacity) {
            $this->error('Capacité par défaut non trouvée. Veuillez exécuter le seeder TruckCapacitySeeder.');
            return;
        }

        DB::transaction(function () use ($defaultCapacity) {
            // Récupérer tous les camions sans capacité
            $trucks = Truck::whereNull('truck_capacity_id')->get();
            
            $count = 0;
            foreach ($trucks as $truck) {
                $this->info("Attribution de la capacité par défaut au camion {$truck->registration_number}");
                
                $truck->truck_capacity_id = $defaultCapacity->id;
                $truck->save();
                
                $count++;
            }
            
            $this->info("Nombre de camions mis à jour : {$count}");
        });
        
        $this->info('Correction des capacités terminée !');
    }
}
