<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Truck;
use App\Models\TruckCapacity;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class TruckController extends Controller
{
    /**
     * Récupérer la liste des camions avec leur capacité
     */
    public function getTrucks()
    {
        try {
            $trucks = DB::table('trucks')
                ->select([
                    'trucks.id',
                    'trucks.registration_number',
                    'drivers.name as driver_name',
                    'drivers.phone as driver_phone',
                    'truck_capacities.tonnage as truck_tonnage'
                ])
                ->leftJoin('drivers', 'trucks.driver_id', '=', 'drivers.id')
                ->leftJoin('truck_capacities', 'trucks.truck_capacity_id', '=', 'truck_capacities.id')
                ->where('trucks.status', 'available')
                ->get();

            // Log pour le débogage
            foreach ($trucks as $truck) {
                Log::info('Données du camion:', [
                    'id' => $truck->id,
                    'immatriculation' => $truck->registration_number,
                    'chauffeur' => $truck->driver_name,
                    'capacité' => $truck->truck_tonnage
                ]);
            }

            $formattedTrucks = $trucks->map(function ($truck) {
                // Préparer le texte à afficher
                $text = $truck->registration_number;
                if ($truck->driver_name) {
                    $text .= " ({$truck->driver_name})";
                }
                if ($truck->truck_tonnage) {
                    $text .= " - {$truck->truck_tonnage}T";
                } else {
                    $text .= " - N/A";
                }

                return [
                    'id' => $truck->id,
                    'text' => $text,
                    'driver' => $truck->driver_name ? [
                        'name' => $truck->driver_name,
                        'phone' => $truck->driver_phone
                    ] : null,
                    'capacity' => $truck->truck_tonnage
                ];
            });

            return response()->json([
                'success' => true,
                'trucks' => $formattedTrucks
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des camions : ' . $e->getMessage());
            Log::error('Stack trace : ' . $e->getTraceAsString());
            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors de la récupération des camions'
            ], 500);
        }
    }

    public function index()
    {
        $trucks = Truck::select([
            'trucks.*',
            'truck_capacities.name as capacity_name',
            'truck_capacities.capacity as capacity_value',
            'truck_capacities.unit as capacity_unit'
        ])
        ->leftJoin('truck_capacities', 'trucks.truck_capacity_id', '=', 'truck_capacities.id')
        ->with('driver')
        ->orderBy('trucks.registration_number')
        ->paginate(10);

        return view('accountant.trucks.index', compact('trucks'));
    }
}
