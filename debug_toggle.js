// Script de débogage pour tester la fonctionnalité toggle-active
// À coller dans la console du navigateur sur la page http://127.0.0.1:8000/admin/users

console.log('=== DÉBOGAGE TOGGLE-ACTIVE ===');

// 1. Vérifier la présence du token CSRF
const csrfToken = document.querySelector('meta[name="csrf-token"]');
console.log('1. Token CSRF trouvé:', csrfToken ? csrfToken.content : 'NON TROUVÉ');

// 2. Vérifier la présence des formulaires toggle-active
const toggleForms = document.querySelectorAll('form[action*="toggle-active"]');
console.log('2. Nombre de formulaires toggle-active trouvés:', toggleForms.length);

// 3. Vérifier SweetAlert2
console.log('3. SweetAlert2 disponible:', typeof Swal !== 'undefined');

// 4. Fonction de test pour un utilisateur spécifique
async function testToggleUser(userId) {
    console.log(`\n=== TEST POUR UTILISATEUR ${userId} ===`);
    
    const form = document.querySelector(`form[action*="/users/${userId}/toggle-active"]`);
    if (!form) {
        console.error(`Formulaire pour utilisateur ${userId} non trouvé`);
        return;
    }
    
    console.log('Formulaire trouvé:', form.action);
    
    try {
        const response = await fetch(form.action, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Accept': 'application/json',
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: new FormData(form)
        });
        
        console.log('Statut de la réponse:', response.status);
        console.log('Headers de la réponse:', Object.fromEntries(response.headers.entries()));
        
        const contentType = response.headers.get('content-type');
        console.log('Type de contenu:', contentType);
        
        if (!response.ok) {
            const errorText = await response.text();
            console.error('Erreur HTTP:', response.status, errorText.substring(0, 500));
            return;
        }
        
        if (contentType && contentType.includes('application/json')) {
            const data = await response.json();
            console.log('Réponse JSON:', data);
            
            if (data.success) {
                console.log('✅ Succès:', data.message);
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        icon: 'success',
                        title: 'Test réussi !',
                        text: data.message,
                        timer: 3000
                    });
                }
            } else {
                console.error('❌ Échec:', data.message);
            }
        } else {
            const text = await response.text();
            console.error('Réponse non-JSON:', text.substring(0, 500));
        }
        
    } catch (error) {
        console.error('Erreur lors de la requête:', error);
    }
}

// 5. Fonction pour tester le premier utilisateur disponible
function testFirstUser() {
    const firstForm = document.querySelector('form[action*="toggle-active"]');
    if (firstForm) {
        const match = firstForm.action.match(/\/users\/(\d+)\/toggle-active/);
        if (match) {
            const userId = match[1];
            console.log(`Test du premier utilisateur trouvé: ${userId}`);
            testToggleUser(userId);
        }
    } else {
        console.error('Aucun formulaire toggle-active trouvé');
    }
}

// 6. Afficher les instructions
console.log('\n=== INSTRUCTIONS ===');
console.log('Pour tester un utilisateur spécifique: testToggleUser(ID_UTILISATEUR)');
console.log('Pour tester le premier utilisateur: testFirstUser()');
console.log('Exemple: testToggleUser(7)');

// 7. Exposer les fonctions globalement
window.testToggleUser = testToggleUser;
window.testFirstUser = testFirstUser;

console.log('\n=== PRÊT POUR LES TESTS ===');
