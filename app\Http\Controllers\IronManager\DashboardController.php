<?php

namespace App\Http\Controllers\IronManager;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:iron_manager']);
    }

    public function index()
    {
        $stats = [
            'total_iron_products' => Product::where('category', 'iron')->count(),
            'total_iron_stock' => Product::where('category', 'iron')->sum('stock_quantity'),
            'low_stock_count' => Product::where('category', 'iron')
                ->where('stock_quantity', '<', 10)
                ->count(),
            'total_iron_value' => Product::where('category', 'iron')
                ->selectRaw('SUM(price * stock_quantity) as total_value')
                ->value('total_value'),
        ];

        $iron_products = Product::where('category', 'iron')
            ->orderBy('stock_quantity')
            ->take(5)
            ->get();

        return view('iron.dashboard', compact('stats', 'iron_products'));
    }
}
