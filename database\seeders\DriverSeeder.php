<?php

namespace Database\Seeders;

use App\Models\Driver;
use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;

class DriverSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $drivers = [
            [
                'first_name' => '<PERSON>',
                'last_name' => '<PERSON><PERSON>',
                'phone_number' => '**********',
                'license_number' => 'DRV001',
                'license_expiry' => Carbon::now()->addYears(2),
                'status' => 'available',
                'address' => '123 Rue de la Paix',
                'emergency_contact' => '<PERSON>',
                'emergency_phone' => '**********',
                'notes' => 'Chauffeur expérimenté'
            ],
            [
                'first_name' => '<PERSON>',
                'last_name' => '<PERSON>',
                'phone_number' => '**********',
                'license_number' => 'DRV002',
                'license_expiry' => Carbon::now()->addYears(1),
                'status' => 'available',
                'address' => '456 Avenue des Lilas',
                'emergency_contact' => '<PERSON>',
                'emergency_phone' => '**********',
                'notes' => 'Spécialiste des longues distances'
            ],
            [
                'first_name' => '<PERSON>',
                'last_name' => '<PERSON>',
                'phone_number' => '**********',
                'license_number' => 'DRV003',
                'license_expiry' => Carbon::now()->addMonths(18),
                'status' => 'available',
                'address' => '789 Boulevard des <PERSON>',
                'emergency_contact' => 'Julie Bernard',
                'emergency_phone' => '**********',
                'notes' => 'Expert en manœuvres difficiles'
            ]
        ];

        foreach ($drivers as $driver) {
            Driver::create($driver);
        }
    }
}
