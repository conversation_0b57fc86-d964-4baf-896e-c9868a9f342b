<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        // 1. Ajouter la nouvelle colonne supplier_id si elle n'existe pas
        if (!Schema::hasColumn('supplies', 'supplier_id')) {
            Schema::table('supplies', function (Blueprint $table) {
                $table->foreignId('supplier_id')->after('reference')->constrained('suppliers');
            });

            // 2. Mettre à jour les approvisionnements avec un fournisseur par défaut
            $defaultSupplierId = DB::table('suppliers')->first()->id;
            DB::table('supplies')->update(['supplier_id' => $defaultSupplierId]);
        }
    }

    public function down()
    {
        if (Schema::hasColumn('supplies', 'supplier_id')) {
            Schema::table('supplies', function (Blueprint $table) {
                $table->dropForeign(['supplier_id']);
                $table->dropColumn('supplier_id');
            });
        }
    }
};
