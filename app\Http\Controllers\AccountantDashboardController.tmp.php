<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Sale;
use App\Models\Payment;
use App\Models\Supply;
use App\Models\Invoice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class AccountantDashboardController extends Controller
{
    /**
     * Affiche le tableau de bord comptable classique (redirige vers le professionnel)
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function index()
    {
        // Pour l'instant, rediriger vers le tableau de bord professionnel
        return redirect()->route('accountant.dashboard.professional');
    }

    /**
     * Affiche le tableau de bord comptable moderne avec quelques graphiques
     *
     * @return \Illuminate\View\View
     */
    public function modernDashboard()
    {
        $stats = $this->calculateDashboardStats();
        
        return view('accountant.dashboard-modern', [
            'stats' => $stats,
            'monthlySales' => $this->getMonthlySalesData(),
            'paymentStats' => $this->getPaymentStatusData(),
            'recentActivities' => $this->getRecentActivities()
        ]);
    }

    /**
     * Affiche le tableau de bord comptable professionnel avec design moderne et animations
     *
     * @return \Illuminate\View\View
     */
    public function professionalDashboard()
    {
        try {
            // Statistiques de base - calculées dynamiquement
            $totalSales = Sale::count();
            $totalRevenue = (int)DB::table('sales')->sum('total_amount');
            $totalPayments = (int)DB::table('payments')->sum('amount');
            $pendingPayments = max(0, $totalRevenue - $totalPayments);
            
            // Récupération des statistiques
            $stats = $this->calculateDashboardStats();
            
            // Statistiques de factures
            $paidInvoices = Sale::where('payment_status', 'paid')->count();
            $partialInvoices = Sale::where('payment_status', 'partial')->count();
            $unpaidInvoices = Sale::where('payment_status', 'unpaid')->count();
            
            // Statistiques pour les cartes "rapides"
            $monthlyCementOrders = $stats['monthly_cement_orders'];
            
            // Créer un tableau pour les données des statistiques
            $stats = [
                'monthly_revenue' => $stats['monthly_revenue'],
                'monthly_cement_orders' => $monthlyCementOrders,
                'paid_invoices' => $paidInvoices,
                'partial_invoices' => $partialInvoices,
                'unpaid_invoices' => $unpaidInvoices
            ];
            
            // Récupération des ventes récentes
            $recentSales = Sale::with('customer')
                ->latest()
                ->take(5)
                ->get();
            
            // Total des factures pour calculer le taux de recouvrement
            $totalInvoices = Sale::count();
            
            // Variables pour les statistiques d'approvisionnement (statiques pour le moment)
            $totalSupplies = 35;
            $validatedSupplies = 22;
            $pendingSupplies = 13;
            $rejectedSupplies = 0;
            $totalSupplyAmount = 5840000;
            
            // Créer un tableau pour les statistiques d'approvisionnement
            $supplyStats = [
                'totalSupplies' => $totalSupplies,
                'validatedSupplies' => $validatedSupplies,
                'pendingSupplies' => $pendingSupplies,
                'rejectedSupplies' => $rejectedSupplies,
                'totalTonnage' => 840.5,
                'validatedTonnage' => 547.8,
                'pendingTonnage' => 250.2,
                'rejectedTonnage' => 42.5,
                'validatedPercentage' => 63,
                'pendingPercentage' => 37,
                'rejectedPercentage' => 0
            ];
            
            // Préparation des données pour les graphiques
            $monthlySales = $this->getMonthlySalesData();
            $paymentStats = $this->getPaymentStatusData();
            $supplyChartData = $this->prepareSupplyChartData();
            
            // Préparer les données à transmettre à la vue
            $viewData = [
                'totalSales' => $totalSales,
                'totalRevenue' => $totalRevenue,
                'totalPayments' => $totalPayments, 
                'pendingPayments' => $pendingPayments,
                'monthlySales' => $monthlySales,
                'paymentStats' => $paymentStats,
                'recentSales' => $recentSales,
                'stats' => $stats,
                'totalInvoices' => $totalInvoices,
                'paidInvoices' => $paidInvoices,
                'partialInvoices' => $partialInvoices,
                'unpaidInvoices' => $unpaidInvoices,
                'supplyStats' => $supplyStats,
                'supplyChartData' => $supplyChartData,
            ];
            
            // Journalisation des données pour débogage
            \Log::debug('Dashboard professionnel - Données transmises à la vue:', $viewData);
            
            return view('accountant.dashboard-professional', $viewData);
            
        } catch (\Exception $e) {
            // En cas d'erreur, initialiser des valeurs par défaut mais enregistrer l'erreur détaillée
            
            // Valeurs par défaut définies explicitement
            $totalSales = 0;
            $totalRevenue = 0;
            $totalPayments = 0;
            $pendingPayments = 0;
            $totalInvoices = 0;
            $paidInvoices = 0;
            $partialInvoices = 0;
            $unpaidInvoices = 0;
            $recentSales = collect([]);
            $monthlySales = ['labels' => [], 'data' => []];
            $paymentStats = ['paid' => 0, 'partial' => 0, 'unpaid' => 0];
            $stats = [
                'monthly_cement_orders' => 0,
                'monthly_revenue' => 0,
                'paid_invoices' => 0,
                'partial_invoices' => 0,
                'unpaid_invoices' => 0
            ];
            $supplyChartData = [
                'labels' => ['Validé', 'En attente', 'Rejeté'],
                'data' => [0, 0, 0],
                'colors' => ['#28a745', '#ffc107', '#dc3545']
            ];
            
            // Valeurs par défaut pour les statistiques d'approvisionnement
            $supplyStats = [
                'totalSupplies' => 0,
                'validatedSupplies' => 0,
                'pendingSupplies' => 0,
                'rejectedSupplies' => 0,
                'totalTonnage' => 0,
                'validatedTonnage' => 0,
                'pendingTonnage' => 0,
                'rejectedTonnage' => 0,
                'validatedPercentage' => 0,
                'pendingPercentage' => 0,
                'rejectedPercentage' => 0
            ];
            
            // Journaliser l'erreur
            \Log::critical('Erreur lors du chargement du tableau de bord professionnel: ' . $e->getMessage());
            
            // Préparer les données pour la vue
            $viewData = [
                'totalSales' => $totalSales,
                'totalRevenue' => $totalRevenue, 
                'totalPayments' => $totalPayments,
                'pendingPayments' => $pendingPayments,
                'monthlySales' => $monthlySales,
                'paymentStats' => $paymentStats,
                'recentSales' => $recentSales,
                'stats' => $stats,
                'totalInvoices' => $totalInvoices,
                'paidInvoices' => $paidInvoices,
                'partialInvoices' => $partialInvoices,
                'unpaidInvoices' => $unpaidInvoices,
                'supplyStats' => $supplyStats,
                'supplyChartData' => $supplyChartData,
            ];
            
            // Vérifier que la vue existe avant de la rendre
            if (view()->exists('accountant.dashboard-professional')) {
                return view('accountant.dashboard-professional', $viewData)
                    ->withErrors(['error' => 'Une erreur est survenue lors du chargement des données.']);
            } else {
                return redirect()->route('accountant.dashboard')
                    ->withErrors(['error' => 'La vue du tableau de bord professionnel est indisponible.']);
            }
        }
    }

    /**
     * Calcule toutes les statistiques nécessaires pour le tableau de bord
     *
     * @return array
     */
    public function calculateDashboardStats()
    {
        $currentMonth = Carbon::now()->month;
        $currentYear = Carbon::now()->year;
        
        $monthlySales = Sale::whereMonth('created_at', $currentMonth)
            ->whereYear('created_at', $currentYear)
            ->count();
            
        $monthlyRevenue = (int)Sale::whereMonth('created_at', $currentMonth)
            ->whereYear('created_at', $currentYear)
            ->sum('total_amount');
            
        $monthlyCementOrders = Sale::whereMonth('created_at', $currentMonth)
            ->whereYear('created_at', $currentYear)
            ->where('product_id', 1) // ID du ciment, à ajuster selon la base de données
            ->count();
            
        $monthlyPayments = (int)Payment::whereMonth('created_at', $currentMonth)
            ->whereYear('created_at', $currentYear)
            ->sum('amount');
            
        $activeCustomers = Sale::whereMonth('created_at', $currentMonth)
            ->whereYear('created_at', $currentYear)
            ->distinct('customer_id')
            ->count('customer_id');
        
        return [
            'monthly_sales' => $monthlySales,
            'monthly_revenue' => $monthlyRevenue,
            'monthly_cement_orders' => $monthlyCementOrders,
            'monthly_payments' => $monthlyPayments,
            'active_customers' => $activeCustomers
        ];
    }
    
    /**
     * Récupère les données des ventes mensuelles pour le graphique
     *
     * @param Carbon|null $startDate Date de début pour filtrer les données
     * @return array
     */
    public function getMonthlySalesData($startDate = null)
    {
        if (!$startDate) {
            $startDate = Carbon::now()->startOfYear();
        }
        
        $months = [];
        $salesData = [];
        
        // Générer les étiquettes des mois
        $currentDate = Carbon::now();
        for ($i = 0; $i < 6; $i++) {
            $monthDate = (clone $currentDate)->subMonths($i);
            $months[] = $monthDate->translatedFormat('F');
            
            // Calculer les ventes pour ce mois
            $monthlySales = Sale::whereMonth('created_at', $monthDate->month)
                ->whereYear('created_at', $monthDate->year)
                ->count();
                
            $salesData[] = $monthlySales;
        }
        
        // Inverser pour avoir l'ordre chronologique
        $months = array_reverse($months);
        $salesData = array_reverse($salesData);
        
        return [
            'labels' => $months,
            'data' => $salesData
        ];
    }
    
    /**
     * Récupère les données des statuts de paiement pour le graphique en donut
     *
     * @param Carbon|null $startDate Date de début pour filtrer les données
     * @return array
     */
    public function getPaymentStatusData($startDate = null)
    {
        if (!$startDate) {
            $startDate = Carbon::now()->subDays(30);
        }
        
        $paid = Sale::where('payment_status', 'paid')
            ->where('created_at', '>=', $startDate)
            ->count();
            
        $partial = Sale::where('payment_status', 'partial')
            ->where('created_at', '>=', $startDate)
            ->count();
            
        $unpaid = Sale::where('payment_status', 'unpaid')
            ->where('created_at', '>=', $startDate)
            ->count();
        
        return [
            'paid' => $paid,
            'partial' => $partial,
            'unpaid' => $unpaid
        ];
    }
    
    /**
     * Prépare les données pour le graphique des approvisionnements
     *
     * @return array
     */
    public function prepareSupplyChartData()
    {
        // Données statiques pour le moment, à remplacer par des données dynamiques
        return [
            'labels' => ['Validé', 'En attente', 'Rejeté'],
            'data' => [22, 13, 0],
            'colors' => ['#28a745', '#ffc107', '#dc3545']
        ];
    }
    
    /**
     * Récupère les activités récentes pour le tableau de bord
     *
     * @param Carbon|null $startDate Date de début pour filtrer les données
     * @return array
     */
    public function getRecentActivities($startDate = null)
    {
        if (!$startDate) {
            $startDate = Carbon::now()->subDays(30);
        }

        $recentSales = Sale::with('customer')
                        ->where('created_at', '>=', $startDate)
                        ->latest()
                        ->take(5)
                        ->get()
                        ->map(function ($sale) {
                            return [
                                'type' => 'sale',
                                'icon' => 'fa-shopping-cart',
                                'color' => 'primary',
                                'id' => $sale->id,
                                'title' => 'Nouvelle vente',
                                'description' => 'Vente #' . $sale->id . ' pour ' . optional($sale->customer)->name,
                                'amount' => $sale->total_amount,
                                'date' => $sale->created_at,
                                'url' => route('accountant.sales.show', $sale->id),
                            ];
                        });
        
        $recentPayments = Payment::with('sale.customer')
                            ->where('created_at', '>=', $startDate)
                            ->latest()
                            ->take(5)
                            ->get()
                            ->map(function ($payment) {
                                return [
                                    'type' => 'payment',
                                    'icon' => 'fa-money-bill-wave',
                                    'color' => 'success',
                                    'id' => $payment->id,
                                    'title' => 'Nouveau paiement',
                                    'description' => 'Paiement #' . $payment->id . ' pour la vente #' . optional($payment->sale)->id,
                                    'amount' => $payment->amount,
                                    'date' => $payment->created_at,
                                    'url' => route('accountant.payments.show', $payment->id),
                                ];
                            });
        
        // Combiner les activités et trier par date
        $allActivities = $recentSales->merge($recentPayments)
                            ->sortByDesc('date')
                            ->take(10)
                            ->values()
                            ->all();
        
        return $allActivities;
    }
    
    /**
     * Récupère les statistiques pour les requêtes AJAX
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats(Request $request)
    {
        $stats = $this->calculateDashboardStats();
        $monthlySales = $this->getMonthlySalesData();
        $paymentStats = $this->getPaymentStatusData();
        
        return response()->json([
            'stats' => $stats,
            'monthlySales' => $monthlySales,
            'paymentStats' => $paymentStats
        ]);
    }
}
