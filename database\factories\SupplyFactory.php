<?php

namespace Database\Factories;

use App\Models\Supply;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

class SupplyFactory extends Factory
{
    /**
     * Le modèle associé à la factory.
     *
     * @var string
     */
    protected $model = Supply::class;

    /**
     * Définir l'état par défaut du modèle.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'reference' => 'APP-' . strtoupper($this->faker->unique()->lexify('????')) . '-' . date('Ymd'),
            'product_id' => Product::factory(),
            'status' => 'pending',
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indiquer que l'approvisionnement est validé.
     */
    public function validated()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'validated',
                'validator_id' => \App\Models\User::factory(),
                'validated_at' => now(),
            ];
        });
    }

    /**
     * Indiquer que l'approvisionnement est rejeté.
     */
    public function rejected()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'rejected',
                'validator_id' => \App\Models\User::factory(),
                'validated_at' => now(),
                'rejection_reason' => $this->faker->sentence(),
            ];
        });
    }
}
