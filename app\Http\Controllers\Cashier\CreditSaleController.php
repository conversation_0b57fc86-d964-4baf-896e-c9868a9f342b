<?php

namespace App\Http\Controllers\Cashier;

use App\Http\Controllers\Controller;
use App\Models\CreditSale;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CreditSaleController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:cashier']);
    }

    public function index()
    {
        $creditSales = CreditSale::with([
            'order',
            'detail.customer',
            'detail.product'
        ])
        ->latest()
        ->paginate(10);

        return view('cashier.credit-sales.index', compact('creditSales'));
    }

    public function show(CreditSale $sale)
    {
        $sale->load([
            'order',
            'detail.customer',
            'detail.product',
            'tripAssignment',
            'payments' => function($query) {
                $query->orderBy('payment_date', 'desc');
            }
        ]);
        
        return view('cashier.credit-sales.show', compact('sale'));
    }

    public function recordPayment(Request $request, CreditSale $sale)
    {
        try {
            DB::beginTransaction();

            // S'assurer que le montant restant est défini
            $remainingAmount = $sale->remaining_amount ?? $sale->amount - $sale->paid_amount;

            // Valider la requête
            $validated = $request->validate([
                'amount' => [
                    'required',
                    'numeric',
                    'min:0',
                    'max:' . $remainingAmount,
                ],
                'payment_method' => [
                    'required',
                    'string',
                    'in:cash,bank_transfer,check'
                ],
                'notes' => [
                    'nullable',
                    'string',
                    'max:500'
                ]
            ]);

            // Créer le paiement
            $payment = $sale->payments()->create([
                'amount' => $validated['amount'],
                'payment_method' => $validated['payment_method'],
                'notes' => $validated['notes'],
                'cashier_id' => auth()->id(),
                'payment_date' => now()
            ]);

            // Mettre à jour les montants de la vente à crédit
            $sale->paid_amount = ($sale->paid_amount ?? 0) + $validated['amount'];
            $sale->remaining_amount = $sale->amount - $sale->paid_amount;
            
            // Mettre à jour le statut
            if ($sale->remaining_amount <= 0) {
                $sale->status = 'paid';
            } elseif ($sale->paid_amount > 0) {
                $sale->status = 'partially_paid';
            }
            
            $sale->save();

            DB::commit();

            return redirect()
                ->route('cashier.credit-sales.show', $sale)
                ->with('success', 'Paiement enregistré avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Une erreur est survenue lors de l\'enregistrement du paiement : ' . $e->getMessage());
        }
    }
}
