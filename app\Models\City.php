<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class City extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'slug', 'region_id'];

    protected $appends = ['product_prices'];

    public function getProductPricesAttribute()
    {
        return $this->attributes['product_prices'] ?? [];
    }

    public function setProductPricesAttribute($value)
    {
        $this->attributes['product_prices'] = $value;
    }

    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class);
    }

    public function productPrices(): HasMany
    {
        return $this->hasMany(ProductPrice::class);
    }

    public function cementOrders(): HasMany
    {
        return $this->hasMany(CementOrder::class);
    }

    public function supplies(): BelongsToMany
    {
        return $this->belongsToMany(Supply::class, 'supply_city')
            ->withPivot(['quantity', 'vehicle_id', 'trips', 'price'])
            ->withTimestamps();
    }

    public function trucks(): BelongsToMany
    {
        return $this->belongsToMany(Truck::class, 'supply_city', 'city_id', 'vehicle_id')
            ->withPivot(['supply_id', 'quantity', 'trips', 'price'])
            ->withTimestamps();
    }

    public function deliveries(): HasMany
    {
        return $this->hasMany(Delivery::class);
    }

    public function getTruckForSupply($supplyId)
    {
        return $this->trucks()
            ->wherePivot('supply_id', $supplyId)
            ->with('driver')
            ->first();
    }
}
