<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Region;
use Illuminate\Http\JsonResponse;

class RegionController extends Controller
{
    /**
     * Récupère les villes d'une région
     *
     * @param Region $region
     * @return JsonResponse
     */
    public function getCities(Region $region): JsonResponse
    {
        $cities = $region->cities()->select('id', 'name')->get();
        return response()->json($cities);
    }
}
