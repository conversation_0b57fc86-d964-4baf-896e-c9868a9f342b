<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Truck;
use App\Models\Driver;
use Illuminate\Support\Facades\DB;

class AssignDriversToTrucks extends Command
{
    protected $signature = 'trucks:assign-drivers';
    protected $description = 'Assigne les chauffeurs disponibles aux camions';

    public function handle()
    {
        $this->info('Assignation des chauffeurs aux camions...');

        DB::beginTransaction();
        try {
            // Récupérer les camions sans chauffeur
            $trucks = Truck::whereNull('driver_id')
                ->where('status', 'assigned')
                ->whereNull('deleted_at')
                ->get();

            // Récupérer les chauffeurs disponibles
            $drivers = Driver::whereNull('deleted_at')
                ->whereNotExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('trucks')
                        ->whereNull('deleted_at')
                        ->whereColumn('trucks.driver_id', 'drivers.id');
                })
                ->get();

            if ($trucks->isEmpty()) {
                $this->error('Aucun camion sans chauffeur trouvé.');
                DB::rollBack();
                return;
            }

            if ($drivers->isEmpty()) {
                $this->error('Aucun chauffeur disponible trouvé.');
                DB::rollBack();
                return;
            }

            $driverIndex = 0;
            foreach ($trucks as $truck) {
                if ($driverIndex >= $drivers->count()) {
                    break;
                }

                $driver = $drivers[$driverIndex];
                $truck->driver_id = $driver->id;
                $truck->save();

                $this->info("Camion {$truck->registration_number} assigné à {$driver->first_name} {$driver->last_name}");
                $driverIndex++;
            }

            DB::commit();
            $this->info('Assignation terminée avec succès !');

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Une erreur est survenue : ' . $e->getMessage());
        }
    }
}
