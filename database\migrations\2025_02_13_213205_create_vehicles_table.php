<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('vehicles', function (Blueprint $table) {
            $table->id();
            $table->string('registration_number')->unique();
            $table->string('brand');
            $table->string('model');
            $table->integer('year');
            $table->decimal('capacity', 8, 2)->comment('Capacity in tons');
            $table->enum('status', ['available', 'assigned', 'maintenance', 'out_of_service'])->default('available');
            $table->datetime('last_maintenance')->nullable();
            $table->datetime('next_maintenance')->nullable();
            $table->foreignId('driver_id')->nullable()->constrained()->nullOnDelete();
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('vehicles');
    }
};
