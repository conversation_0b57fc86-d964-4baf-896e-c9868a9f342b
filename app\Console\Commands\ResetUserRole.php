<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;

class ResetUserRole extends Command
{
    protected $signature = 'user:reset-role {email} {role}';
    protected $description = 'Reset user role by removing all roles and assigning a new one';

    public function handle()
    {
        $email = $this->argument('email');
        $roleName = $this->argument('role');

        // Trouver l'utilisateur
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("Utilisateur avec l'email {$email} non trouvé.");
            return 1;
        }

        // Vérifier si le rôle existe
        $role = Role::where('name', $roleName)->first();
        
        if (!$role) {
            // Créer le rôle s'il n'existe pas
            $role = Role::create(['name' => $roleName]);
            $this->info("Rôle {$roleName} créé.");
        }

        // Supprimer tous les rôles actuels
        $user->syncRoles([]);
        $this->info('Tous les rôles ont été supprimés.');

        // Assigner le nouveau rôle
        $user->assignRole($roleName);
        $this->info("Le rôle {$roleName} a été assigné à l'utilisateur {$email}.");
        
        // Vérifier les rôles actuels
        $this->info("\nRôles actuels de l'utilisateur :");
        foreach ($user->roles as $userRole) {
            $this->info("- " . $userRole->name);
        }
        
        return 0;
    }
}
