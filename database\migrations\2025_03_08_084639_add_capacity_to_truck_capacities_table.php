<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('truck_capacities', function (Blueprint $table) {
            if (!Schema::hasColumn('truck_capacities', 'capacity')) {
                $table->decimal('capacity', 10, 2)->after('name');
            }
            if (!Schema::hasColumn('truck_capacities', 'unit')) {
                $table->string('unit')->after('capacity');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('truck_capacities', function (Blueprint $table) {
            if (Schema::hasColumn('truck_capacities', 'unit')) {
                $table->dropColumn('unit');
            }
            if (Schema::hasColumn('truck_capacities', 'capacity')) {
                $table->dropColumn('capacity');
            }
        });
    }
};
