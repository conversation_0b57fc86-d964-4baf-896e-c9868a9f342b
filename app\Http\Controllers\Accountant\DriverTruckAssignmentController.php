<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Driver;
use App\Models\Truck;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class DriverTruckAssignmentController extends Controller
{
    public function index()
    {
        $drivers = Driver::with(['truck', 'truck.capacity'])->get();
        $trucks = Truck::with('capacity')
            ->where('status', 'available')
            ->orWhereHas('driver')
            ->get();
        
        return view('accountant.driver-truck-assignments.index', compact('drivers', 'trucks'));
    }

    /**
     * Assigner un véhicule à un chauffeur
     */
    public function update(Request $request)
    {
        try {
            $validated = $request->validate([
                'driver_id' => 'required|exists:drivers,id',
                'truck_id' => 'required|exists:trucks,id',
            ]);

            DB::beginTransaction();

            $driver = Driver::findOrFail($validated['driver_id']);
            $truck = Truck::findOrFail($validated['truck_id']);

            // Désassigner tous les autres camions de ce chauffeur
            Truck::where('driver_id', $driver->id)
                ->update(['driver_id' => null, 'status' => 'available']);

            // Désassigner ce camion de tout autre chauffeur
            if ($truck->driver_id !== null && $truck->driver_id !== $driver->id) {
                $truck->driver_id = null;
                $truck->status = 'available';
                $truck->save();
            }

            // Assigner le nouveau camion
            $truck->driver_id = $driver->id;
            $truck->status = 'assigned';
            $truck->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Le véhicule a été assigné avec succès.',
                'truck' => [
                    'id' => $truck->id,
                    'registration_number' => $truck->registration_number,
                    'status' => $truck->status
                ]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de l\'assignation du véhicule : ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => $e instanceof ValidationException 
                    ? current($e->errors())[0]
                    : 'Une erreur est survenue lors de l\'assignation du véhicule.',
            ], $e instanceof ValidationException ? 422 : 500);
        }
    }

    /**
     * Retirer l'assignation d'un véhicule
     */
    public function unassign(Request $request)
    {
        try {
            $validated = $request->validate([
                'driver_id' => 'required|exists:drivers,id',
            ]);

            DB::beginTransaction();

            $driver = Driver::findOrFail($validated['driver_id']);
            
            // Si le chauffeur a un camion, on retire l'assignation
            if ($driver->truck) {
                $truck = $driver->truck;
                $truck->driver_id = null;
                $truck->status = 'available';
                $truck->save();
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'L\'assignation a été retirée avec succès.'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors du retrait de l\'assignation : ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => $e instanceof ValidationException 
                    ? current($e->errors())[0]
                    : 'Une erreur est survenue lors du retrait de l\'assignation.',
            ], $e instanceof ValidationException ? 422 : 500);
        }
    }
}
