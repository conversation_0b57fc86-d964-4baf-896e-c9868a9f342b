<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('supply_details', function (Blueprint $table) {
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');
            $table->dropForeign('supply_details_supply_id_foreign');
            $table->dropForeign('supply_details_product_id_foreign');
            $table->dropForeign('supply_details_region_id_foreign');
            $table->dropColumn(['supply_id', 'product_id', 'region_id', 'quantity', 'unit_price', 'total_price', 'tonnage', 'status', 'created_at', 'updated_at', 'deleted_at']);
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        });

        Schema::table('supply_details', function (Blueprint $table) {
            $table->foreignId('supply_id')->constrained('supplies')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->foreignId('region_id')->constrained('regions')->onDelete('cascade');
            $table->decimal('quantity', 10, 2);
            $table->decimal('unit_price', 10, 2);
            $table->decimal('total_price', 12, 2);
            $table->decimal('tonnage', 10, 2);
            $table->string('status')->default('pending');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        // No need for down method as we're just modifying column definitions
    }
};
