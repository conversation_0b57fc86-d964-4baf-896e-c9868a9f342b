<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Customer;
use App\Models\Sale;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Barryvdh\DomPDF\Facade\Pdf;

class InvoiceController extends Controller
{
    /**
     * Affiche la liste des factures
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        try {
            // Récupère les factures (commandes/ventes complétées)
            $query = Sale::with(['customer'])
                ->where(function($q) {
                    // On considère comme factures les ventes achevées
                    $q->where('status', 'completed')
                      ->orWhereNotNull('invoice_number');
                });
                
            // Filtre par statut de paiement si demandé
            if ($request->has('payment_status') && !empty($request->payment_status)) {
                $query->where('payment_status', $request->payment_status);
            }
            
            // Filtre par recherche
            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('invoice_number', 'like', "%{$search}%")
                      ->orWhere('id', 'like', "%{$search}%")
                      ->orWhereHas('customer', function($q) use ($search) {
                          $q->where('name', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%");
                      });
                });
            }
                
            // Pagination des résultats
            $invoices = $query->latest()->paginate(15);
            
            // Statistiques pour le tableau de bord des factures
            $totalInvoices = $query->count();
            $paidInvoices = $query->where('payment_status', 'paid')->count();
            $partialInvoices = $query->where('payment_status', 'partial')->count();
            $unpaidInvoices = $query->where('payment_status', 'unpaid')->count();
            
            // Liste des clients pour le filtre
            $customers = Customer::orderBy('name')->get();

            return view('accountant.invoices.index', compact(
                'invoices', 'totalInvoices', 'paidInvoices', 'partialInvoices', 'unpaidInvoices', 'customers'
            ));
        } catch (\Exception $e) {
            Log::error('Erreur dans InvoiceController@index: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement des factures.');
        }
    }
    
    /**
     * Affiche le formulaire de création d'une facture
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        try {
            // Récupère les ventes qui n'ont pas de facture
            $sales = Sale::whereNull('invoice_number')
                ->with('customer')
                ->latest()
                ->get();
            
            // Liste des clients
            $customers = Customer::orderBy('name')->get();
            
            return view('accountant.invoices.create', compact('sales', 'customers'));
        } catch (\Exception $e) {
            Log::error('Erreur dans InvoiceController@create: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement du formulaire de création de facture.');
        }
    }
    
    /**
     * Enregistre une nouvelle facture
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'sale_id' => 'required|exists:sales,id',
            'invoice_date' => 'required|date',
            'due_date' => 'required|date|after_or_equal:invoice_date',
        ]);
        
        try {
            DB::beginTransaction();
            
            // Récupère la vente associée
            $sale = Sale::findOrFail($request->sale_id);
            
            // Génère un numéro de facture unique
            $latestInvoice = Sale::whereNotNull('invoice_number')->latest('invoice_number')->first();
            $invoiceNumber = $latestInvoice ? 'INV-' . str_pad((intval(substr($latestInvoice->invoice_number, 4)) + 1), 6, '0', STR_PAD_LEFT) : 'INV-000001';
            
            // Met à jour la vente avec les informations de facture
            $sale->invoice_number = $invoiceNumber;
            $sale->invoice_date = $request->invoice_date;
            $sale->due_date = $request->due_date;
            $sale->notes = $request->notes;
            $sale->status = 'completed';
            $sale->save();
            
            DB::commit();
            
            return redirect()->route('accountant.invoices.show', $sale->id)
                ->with('success', 'Facture créée avec succès.');
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Erreur dans InvoiceController@store: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la création de la facture: ' . $e->getMessage());
        }
    }

    /**
     * Affiche les détails d'une facture
     *
     * @param Sale $invoice
     * @return \Illuminate\View\View
     */
    public function show(Sale $invoice)
    {
        try {
            $invoice->load(['customer', 'saleItems.product', 'payments']);
            
            // Calcul du montant payé et restant à payer
            $totalPaid = $invoice->payments->sum('amount');
            $balanceDue = $invoice->total_amount - $totalPaid;
            
            return view('accountant.invoices.show', compact('invoice', 'totalPaid', 'balanceDue'));
        } catch (\Exception $e) {
            Log::error('Erreur dans InvoiceController@show: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement de la facture.');
        }
    }
    
    /**
     * Affiche le formulaire d'édition d'une facture
     *
     * @param Sale $invoice
     * @return \Illuminate\View\View
     */
    public function edit(Sale $invoice)
    {
        try {
            $invoice->load(['customer', 'saleItems.product']);
            return view('accountant.invoices.edit', compact('invoice'));
        } catch (\Exception $e) {
            Log::error('Erreur dans InvoiceController@edit: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du chargement du formulaire d\'édition.');
        }
    }
    
    /**
     * Met à jour une facture existante
     *
     * @param Request $request
     * @param Sale $invoice
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Sale $invoice)
    {
        $request->validate([
            'invoice_date' => 'required|date',
            'due_date' => 'required|date',
        ]);
        
        try {
            DB::beginTransaction();
            
            $invoice->invoice_date = $request->invoice_date;
            $invoice->due_date = $request->due_date;
            $invoice->notes = $request->notes;
            $invoice->save();
            
            DB::commit();
            
            return redirect()->route('accountant.invoices.show', $invoice->id)
                ->with('success', 'Facture mise à jour avec succès.');
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Erreur dans InvoiceController@update: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la mise à jour de la facture.');
        }
    }
    
    /**
     * Supprime une facture
     *
     * @param Sale $invoice
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Sale $invoice)
    {
        try {
            // On ne supprime pas la vente, on supprime juste les informations de facturation
            DB::beginTransaction();
            
            $invoice->invoice_number = null;
            $invoice->invoice_date = null;
            $invoice->due_date = null;
            $invoice->save();
            
            DB::commit();
            
            return redirect()->route('accountant.invoices.index')
                ->with('success', 'Facture supprimée avec succès.');
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Erreur dans InvoiceController@destroy: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la suppression de la facture.');
        }
    }
    
    /**
     * Envoie la facture par email au client
     *
     * @param Sale $invoice
     * @return \Illuminate\Http\RedirectResponse
     */
    public function send(Sale $invoice)
    {
        try {
            $invoice->load(['customer', 'saleItems.product']);
            
            // Vérifie si le client a un email
            if (!$invoice->customer || !$invoice->customer->email) {
                return back()->with('error', 'Le client n\'a pas d\'adresse email valide.');
            }
            
            // Génère le PDF
            $pdf = PDF::loadView('accountant.invoices.pdf', compact('invoice'));
            $pdfPath = storage_path('app/public/invoices/facture-' . $invoice->invoice_number . '.pdf');
            
            // Assure que le répertoire existe
            if (!file_exists(dirname($pdfPath))) {
                mkdir(dirname($pdfPath), 0755, true);
            }
            
            // Sauvegarde le PDF
            $pdf->save($pdfPath);
            
            // TODO: Implémentation de l'envoi d'email (dépend de la configuration mail du système)
            // Mail::send('emails.invoice', ['invoice' => $invoice], function($message) use ($invoice, $pdfPath) {
            //     $message->to($invoice->customer->email, $invoice->customer->name)
            //         ->subject('Facture #' . $invoice->invoice_number)
            //         ->attach($pdfPath);
            // });
            
            return back()->with('success', 'La facture sera envoyée au client prochainement.');
        } catch (\Exception $e) {
            Log::error('Erreur dans InvoiceController@send: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de l\'envoi de la facture: ' . $e->getMessage());
        }
    }

    /**
     * Génère un PDF de la facture et l'affiche
     *
     * @param Sale $invoice
     * @return \Illuminate\Http\Response
     */
    public function print(Sale $invoice)
    {
        try {
            $invoice->load(['customer', 'saleItems.product']);
            $pdf = PDF::loadView('accountant.invoices.pdf', compact('invoice'));
            return $pdf->stream('facture-' . ($invoice->invoice_number ?? $invoice->id) . '.pdf');
        } catch (\Exception $e) {
            Log::error('Erreur dans InvoiceController@print: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors de la génération de la facture: ' . $e->getMessage());
        }
    }
    
    /**
     * Génère un PDF de la facture et le télécharge
     *
     * @param Sale $invoice
     * @return \Illuminate\Http\Response
     */
    public function download(Sale $invoice)
    {
        try {
            $invoice->load(['customer', 'saleItems.product']);
            $pdf = PDF::loadView('accountant.invoices.pdf', compact('invoice'));
            return $pdf->download('facture-' . ($invoice->invoice_number ?? $invoice->id) . '.pdf');
        } catch (\Exception $e) {
            Log::error('Erreur dans InvoiceController@download: ' . $e->getMessage());
            return back()->with('error', 'Une erreur est survenue lors du téléchargement de la facture: ' . $e->getMessage());
        }
    }
}
