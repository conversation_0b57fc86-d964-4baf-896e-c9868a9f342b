<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class PaymentsExport implements FromCollection, WithHeadings, WithStyles, WithTitle
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        $collection = collect();
        
        // Statistiques
        $collection->push([
            'STATISTIQUES GÉNÉRALES', '', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Total des paiements', number_format($this->data['stats']['total_payments']), '', '', '', '', '', ''
        ]);
        $collection->push([
            'Montant total', number_format($this->data['stats']['total_amount'], 0, ',', ' ') . ' FCFA', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Paiements en espèces', number_format($this->data['stats']['cash_payments'], 0, ',', ' ') . ' FCFA', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Paiements par virement', number_format($this->data['stats']['transfer_payments'], 0, ',', ' ') . ' FCFA', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Montant en attente', number_format($this->data['stats']['pending_amount'], 0, ',', ' ') . ' FCFA', '', '', '', '', '', ''
        ]);
        $collection->push(['', '', '', '', '', '', '', '']); // Ligne vide

        // Répartition par méthode
        $collection->push([
            'RÉPARTITION PAR MÉTHODE DE PAIEMENT', '', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Méthode', 'Montant (FCFA)', 'Nombre', 'Pourcentage (%)', '', '', '', ''
        ]);

        foreach ($this->data['payment_methods'] as $method) {
            $percentage = $this->data['stats']['total_amount'] > 0 ? ($method->total / $this->data['stats']['total_amount']) * 100 : 0;
            $methodName = $this->getPaymentMethodText($method->payment_method);
            
            $collection->push([
                $methodName,
                number_format($method->total, 0, ',', ' '),
                number_format($method->count),
                number_format($percentage, 1),
                '',
                '',
                '',
                ''
            ]);
        }

        $collection->push(['', '', '', '', '', '', '', '']); // Ligne vide

        // En-têtes des paiements
        $collection->push([
            'LISTE DES PAIEMENTS', '', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Date', 'Référence', 'Vente', 'Montant (FCFA)', 'Méthode', 'Statut', 'Caissier', 'Réf. externe'
        ]);

        // Données des paiements
        foreach ($this->data['payments'] as $payment) {
            $collection->push([
                $payment->payment_date->format('d/m/Y H:i'),
                $payment->reference,
                $payment->sale ? ($payment->sale->invoice_number ?? 'Vente #' . $payment->sale->id) : 'N/A',
                number_format($payment->amount, 0, ',', ' '),
                $this->getPaymentMethodText($payment->payment_method),
                $this->getStatusText($payment->status),
                $payment->cashier ? $payment->cashier->name : 'N/A',
                $payment->reference_number ?? '-'
            ]);
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'RAPPORT DES PAIEMENTS',
            'Période: ' . \Carbon\Carbon::parse($this->data['start_date'])->format('d/m/Y') . ' - ' . \Carbon\Carbon::parse($this->data['end_date'])->format('d/m/Y'),
            '',
            '',
            '',
            '',
            '',
            ''
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 16,
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => 'FFF6C23E',
                    ],
                ],
            ],
            2 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],
        ];
    }

    public function title(): string
    {
        return 'Paiements';
    }

    private function getPaymentMethodText($method)
    {
        switch ($method) {
            case 'cash':
                return 'Espèces';
            case 'bank_transfer':
                return 'Virement bancaire';
            case 'check':
                return 'Chèque';
            case 'mobile_money':
                return 'Mobile Money';
            default:
                return $method;
        }
    }

    private function getStatusText($status)
    {
        switch ($status) {
            case 'completed':
                return 'Terminé';
            case 'pending':
                return 'En attente';
            case 'cancelled':
                return 'Annulé';
            default:
                return $status;
        }
    }
}
