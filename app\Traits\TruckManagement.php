<?php

namespace App\Traits;

use App\Models\Truck;
use Illuminate\Http\JsonResponse;

trait TruckManagement
{
    /**
     * Récupère la liste des véhicules avec leurs chauffeurs
     */
    public function getTrucksList(): JsonResponse
    {
        try {
            $trucks = Truck::with(['capacity', 'driver'])
                ->select('trucks.*')
                ->where(function($query) {
                    $query->where('status', 'available')
                          ->orWhere('status', 'assigned');
                })
                ->whereHas('driver')
                ->whereHas('capacity')
                ->get()
                ->map(function ($truck) {
                    return [
                        'id' => $truck->id,
                        'registration_number' => $truck->registration_number,
                        'driver' => [
                            'first_name' => $truck->driver->first_name,
                            'last_name' => $truck->driver->last_name
                        ],
                        'capacity' => $truck->capacity->capacity . ' tonnes'
                    ];
                });

            return response()->json([
                'success' => true,
                'trucks' => $trucks
            ]);
        } catch (\Exception $e) {
            \Log::error('Erreur lors de la récupération des camions', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des camions'
            ], 500);
        }
    }

    /**
     * Récupère la liste des véhicules assignés
     */
    public function getAssignedTrucks(): JsonResponse
    {
        try {
            $trucks = Truck::with(['capacity', 'driver'])
                ->select('trucks.*')
                ->where('status', 'assigned')
                ->whereHas('driver')
                ->whereHas('capacity')
                ->get()
                ->map(function ($truck) {
                    return [
                        'id' => $truck->id,
                        'registration_number' => $truck->registration_number,
                        'driver' => [
                            'first_name' => $truck->driver->first_name,
                            'last_name' => $truck->driver->last_name
                        ],
                        'capacity' => $truck->capacity->capacity . ' tonnes'
                    ];
                });

            return response()->json([
                'success' => true,
                'trucks' => $trucks
            ]);
        } catch (\Exception $e) {
            \Log::error('Erreur lors de la récupération des camions assignés', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des camions assignés'
            ], 500);
        }
    }
}
