<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Supply;
use App\Models\Supplier;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('supplies')
            ->whereNull('supplier')
            ->orWhere('supplier', '')
            ->update(['supplier' => 'GRANUTOGO']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Rien à faire ici
    }
};
