<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Paramètres généraux de l'application
        $generalSettings = [
            [
                'key' => 'app_name',
                'value' => 'GRADIS',
                'group' => 'general',
                'type' => 'string',
                'description' => 'Nom de l\'application'
            ],
            [
                'key' => 'app_email',
                'value' => '<EMAIL>',
                'group' => 'general',
                'type' => 'string',
                'description' => 'Email de l\'application'
            ],
            [
                'key' => 'app_currency',
                'value' => 'FCFA',
                'group' => 'general',
                'type' => 'string',
                'description' => 'Devise par défaut'
            ],
            [
                'key' => 'tax_rate',
                'value' => '18',
                'group' => 'general',
                'type' => 'numeric',
                'description' => 'Taux de TVA (%)'
            ],
            [
                'key' => 'backup_enabled',
                'value' => '1',
                'group' => 'general',
                'type' => 'boolean',
                'description' => 'Sauvegardes automatiques activées'
            ]
        ];

        // Paramètres de comptabilité
        $accountingSettings = [
            [
                'key' => 'default_payment_terms',
                'value' => json_encode(30),
                'group' => 'accounting',
                'type' => 'integer',
                'description' => 'Délai de paiement par défaut (en jours)'
            ],
            [
                'key' => 'default_currency',
                'value' => json_encode('FCFA'),
                'group' => 'accounting',
                'type' => 'string',
                'description' => 'Devise par défaut'
            ],
            [
                'key' => 'vat_rate',
                'value' => json_encode(18),
                'group' => 'accounting',
                'type' => 'integer',
                'description' => 'Taux de TVA (%)'
            ],
            [
                'key' => 'invoice_prefix',
                'value' => json_encode('FAC-'),
                'group' => 'accounting',
                'type' => 'string',
                'description' => 'Préfixe des numéros de facture'
            ]
        ];

        // Insérer les paramètres généraux
        foreach ($generalSettings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key'], 'group' => $setting['group']],
                $setting
            );
        }

        // Insérer les paramètres de comptabilité
        foreach ($accountingSettings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key'], 'group' => $setting['group']],
                $setting
            );
        }
    }
}
