<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // D'abord, mettre à jour toutes les valeurs existantes pour s'assurer qu'elles sont valides
        DB::table('trucks')->where('status', '!=', 'available')->update(['status' => 'assigned']);
        
        // Modifier la colonne status pour utiliser un string
        Schema::table('trucks', function (Blueprint $table) {
            if (Schema::hasColumn('trucks', 'status')) {
                $table->string('status')->default('available')->change();
            } else {
                $table->string('status')->default('available');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trucks', function (Blueprint $table) {
            if (Schema::hasColumn('trucks', 'status')) {
                $table->string('status')->default('available')->change();
            }
        });
    }
};
