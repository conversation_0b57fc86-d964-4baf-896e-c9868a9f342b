<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\City;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class CityController extends Controller
{
    public function __construct()
    {
        $this->middleware('api');
    }

    public function list(): JsonResponse
    {
        try {
            Log::info('Début de la récupération des villes');
            
            $cities = City::select('id', 'name', 'region_id')
                         ->orderBy('name')
                         ->get();
            
            Log::info('Villes récupérées avec succès', ['count' => $cities->count()]);
            
            return response()->json($cities)
                   ->header('Content-Type', 'application/json');
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des villes', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(
                ['error' => 'Une erreur est survenue lors de la récupération des villes'],
                500
            )->header('Content-Type', 'application/json');
        }
    }
}
