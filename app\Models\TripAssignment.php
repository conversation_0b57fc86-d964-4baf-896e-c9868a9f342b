<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TripAssignment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'cement_order_id',
        'cement_order_detail_id',
        'trip_id',
        'truck_id',
        'driver_id',
        'trip_number',
        'tonnage',
        'status',
        'assigned_at',
        'completed_at',
        'start_date',
        'end_date',
        'notes'
    ];

    protected $casts = [
        'assigned_at' => 'datetime',
        'completed_at' => 'datetime',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'tonnage' => 'decimal:2'
    ];

    public function cementOrder(): BelongsTo
    {
        return $this->belongsTo(CementOrder::class);
    }

    public function cementOrderDetail(): BelongsTo
    {
        return $this->belongsTo(CementOrderDetail::class);
    }

    public function trip(): BelongsTo
    {
        return $this->belongsTo(Trip::class);
    }

    public function truck(): BelongsTo
    {
        return $this->belongsTo(Truck::class);
    }

    public function driver(): BelongsTo
    {
        return $this->belongsTo(Driver::class, 'driver_id')->withTrashed();
    }

    // Accessors
    public function getTripReferenceAttribute()
    {
        return 'V' . str_pad($this->trip_number ?? 0, 3, '0', STR_PAD_LEFT);
    }

    public function getTruckNumberAttribute()
    {
        if ($this->relationLoaded('truck') && $this->truck) {
            return $this->truck->registration_number;
        }
        return 'Non assigné';
    }

    public function getDriverNameAttribute()
    {
        if ($this->relationLoaded('driver') && $this->driver) {
            return $this->driver->first_name . ' ' . $this->driver->last_name;
        }
        return 'Non assigné';
    }
}
