<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Assignment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'cement_order_id',
        'cement_order_detail_id',
        'truck_id',
        'driver_id',
        'trip_number',
        'tonnage',
        'start_date',
        'end_date',
        'status',
        'notes'
    ];

    protected $casts = [
        'trip_number' => 'integer',
        'tonnage' => 'decimal:2',
        'start_date' => 'datetime',
        'end_date' => 'datetime'
    ];

    // Relations
    public function cementOrder(): BelongsTo
    {
        return $this->belongsTo(CementOrder::class);
    }

    public function cementOrderDetail(): BelongsTo
    {
        return $this->belongsTo(CementOrderDetail::class);
    }

    public function truck(): BelongsTo
    {
        return $this->belongsTo(Truck::class);
    }

    public function driver(): BelongsTo
    {
        return $this->belongsTo(Driver::class);
    }

    // Scopes pour le statut
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    // Accesseurs
    public function getFormattedTonnageAttribute(): string
    {
        return number_format($this->tonnage, 2, ',', ' ') . ' T';
    }

    public function getFormattedStartDateAttribute(): string
    {
        return $this->start_date ? $this->start_date->format('d/m/Y H:i') : '-';
    }

    public function getFormattedEndDateAttribute(): string
    {
        return $this->end_date ? $this->end_date->format('d/m/Y H:i') : '-';
    }

    public function getFormattedStatusAttribute(): string
    {
        return match($this->status) {
            'pending' => 'En attente',
            'in_progress' => 'En cours',
            'completed' => 'Terminé',
            'cancelled' => 'Annulé',
            default => $this->status
        };
    }
}
