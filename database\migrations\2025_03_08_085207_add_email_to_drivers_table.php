<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('drivers', function (Blueprint $table) {
            if (!Schema::hasColumn('drivers', 'email')) {
                $table->string('email')->nullable()->after('last_name');
            }
            if (!Schema::hasColumn('drivers', 'phone_number')) {
                $table->string('phone_number')->nullable()->after('email');
            }
            if (!Schema::hasColumn('drivers', 'address')) {
                $table->text('address')->nullable()->after('phone_number');
            }
        });

        // Mettre à jour les emails existants pour éviter les doublons
        DB::table('drivers')->whereNull('email')->update([
            'email' => DB::raw('CONCAT(first_name, ".", last_name, "@example.com")')
        ]);

        // Ajouter la contrainte unique après avoir mis à jour les données
        Schema::table('drivers', function (Blueprint $table) {
            if (Schema::hasColumn('drivers', 'email')) {
                $table->unique('email');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('drivers', function (Blueprint $table) {
            if (Schema::hasColumn('drivers', 'email')) {
                $table->dropUnique(['email']);
            }
            if (Schema::hasColumn('drivers', 'address')) {
                $table->dropColumn('address');
            }
            if (Schema::hasColumn('drivers', 'phone_number')) {
                $table->dropColumn('phone_number');
            }
            if (Schema::hasColumn('drivers', 'email')) {
                $table->dropColumn('email');
            }
        });
    }
};
