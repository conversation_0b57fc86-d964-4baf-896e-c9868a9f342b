<?php

namespace App\Http\Controllers\Cashier;

use App\Http\Controllers\Controller;
use App\Models\CementOrder;
use App\Models\CementOrderDetail;
use App\Models\CementOrderPayment;
use App\Models\CreditSale;
use App\Models\Product;
use App\Models\ProductPrice;
use App\Models\Trip;
use App\Models\TripAssignment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use App\Models\Category;
use App\Models\User;
use App\Models\City;
use App\Models\TruckCapacity;

class CementOrderController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:cashier']);
    }

    public function index()
    {
        try {
            Log::info('Début de la récupération des commandes pour le caissier');

            // Récupérer toutes les commandes qui ont des affectations
            $orders = CementOrder::select([
                'cement_orders.id',
                'cement_orders.reference',
                'cement_orders.status',
                'cement_orders.created_at',
                'cement_orders.product_id'
            ])
            ->with([
                'product:id,name,unit',
                'details' => function($query) {
                    $query->select([
                        'cement_order_details.id', 
                        'cement_order_details.cement_order_id',
                        'cement_order_details.product_id', 
                        'cement_order_details.destination_id',
                        'cement_order_details.customer_id',
                        'cement_order_details.quantity',
                        'cement_order_details.unit_price',
                        'cement_order_details.total_price',
                        'cement_order_details.delivered_quantity',
                        'cement_order_details.remaining_quantity',
                        'cement_order_details.status',
                        'cement_order_details.trips_count',
                        'cement_order_details.tonnage_per_trip',
                        'cement_order_details.capacity'
                    ])
                    ->with([
                        'product:id,name,unit',
                        'destination:id,name',
                        'customer:id,name',
                        'tripAssignments'
                    ])
                    ->whereHas('tripAssignments');
                }
            ])
            ->whereHas('details.tripAssignments')
            ->latest()
            ->paginate(10);

            // Pour chaque détail, récupérer le prix correspondant
            foreach ($orders as $order) {
                foreach ($order->details as $detail) {
                    if ($detail->product_id && $detail->destination_id) {
                        $price = ProductPrice::where('product_id', $detail->product_id)
                            ->where('destination_id', $detail->destination_id)
                            ->value('price') ?? 0;
                        
                        $detail->price = $price;
                        $detail->total_price = $price * $detail->quantity;
                        
                        Log::info("Détail #{$detail->id} - Prix: {$price}, Total: {$detail->total_price}", [
                            'product_id' => $detail->product_id,
                            'destination_id' => $detail->destination_id
                        ]);
                    } else {
                        Log::warning("Détail #{$detail->id} - Données manquantes", [
                            'product_id' => $detail->product_id,
                            'destination_id' => $detail->destination_id
                        ]);
                        $detail->price = 0;
                        $detail->total_price = 0;
                    }
                }
            }

            return view('cashier.cement-orders.index', compact('orders'));
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des commandes : ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            return back()->with('error', 'Une erreur est survenue lors de la récupération des commandes : ' . $e->getMessage());
        }
    }

    public function show(CementOrder $order)
    {
        // Recharger l'ordre avec une requête plus directe
        $order = CementOrder::where('id', $order->id)
            ->with([
                'product',
                'details' => function($query) {
                    $query->with(['product']);
                },
                'details.customer',
                'details.destination',
                'details.creditSales',
                'details.tripAssignments' => function($query) {
                    $query->select([
                        'trip_assignments.*',
                        \DB::raw('(SELECT registration_number FROM trucks WHERE trucks.id = trip_assignments.truck_id) as truck_number'),
                        'drivers.first_name as driver_first_name',
                        'drivers.last_name as driver_last_name'
                    ])
                    ->leftJoin('drivers', 'trip_assignments.driver_id', '=', 'drivers.id')
                    ->orderBy('trip_assignments.id');
                }
            ])
            ->firstOrFail();

        $totalAmount = 0;
        $totalPaid = 0;
        
        // Calculer les montants pour chaque détail
        foreach ($order->details as $detail) {
            // Récupérer le prix du produit pour cette ville
            $price = ProductPrice::where('product_id', $detail->product_id)
                ->where('destination_id', $detail->destination_id)
                ->value('price') ?? 0;
                
            // Calculer le prix total
            $detail->price = $price;
            $detail->total_price = $price * $detail->tonnage;
            $totalAmount += $detail->total_price;
            
            // Calculer le montant payé pour ce détail
            $totalPaid += $detail->creditSales->sum('amount_paid');
        }
        
        // Ajouter les totaux à l'objet commande
        $order->total_amount = $totalAmount;
        $order->paid_amount = $totalPaid;
        $order->remaining_amount = $totalAmount - $totalPaid;
        
        return view('cashier.cement-orders.show', compact('order'));
    }

    public function convertToCreditSale(CementOrder $order, Request $request)
    {
        try {
            Log::info('Début de la conversion de la commande #' . $order->id . ' en vente à crédit');
            Log::info('Données reçues:', $request->all());
            
            DB::beginTransaction();

            // Valider la requête
            $validated = $request->validate([
                'trip_assignment_id' => 'nullable|exists:trip_assignments,id',
                'due_date' => 'nullable|date|after:today'
            ]);

            if ($request->has('trip_assignment_id')) {
                // Si un trip_assignment_id est fourni, convertir uniquement ce voyage
                $tripAssignment = TripAssignment::findOrFail($validated['trip_assignment_id']);
                
                // Vérifier que le voyage n'est pas déjà converti
                if ($tripAssignment->status === 'converted_to_sale') {
                    throw new \Exception('Ce voyage a déjà été converti en vente.');
                }

                $this->createCreditSaleForTrip($order, $tripAssignment, $validated['due_date'] ?? null);
            } else {
                // Si aucun trip_assignment_id n'est fourni, convertir tous les voyages non convertis
                foreach ($order->details as $detail) {
                    $unconvertedTrips = $detail->tripAssignments()->where('status', 'validated')->get();
                    foreach ($unconvertedTrips as $tripAssignment) {
                        $this->createCreditSaleForTrip($order, $tripAssignment, $validated['due_date'] ?? null);
                    }
                }
            }

            DB::commit();
            return back()->with('success', 'La conversion en vente à crédit a été effectuée avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de la conversion : ' . $e->getMessage());
            return back()->with('error', 'Erreur lors de la conversion : ' . $e->getMessage());
        }
    }

    private function createCreditSaleForTrip(CementOrder $order, TripAssignment $tripAssignment, $dueDate = null)
    {
        // Charger les relations nécessaires
        $tripAssignment->load(['cementOrderDetail.product', 'cementOrderDetail.destination']);
        
        // Récupérer le prix
        $price = ProductPrice::where('product_id', $tripAssignment->cementOrderDetail->product_id)
            ->where('destination_id', $tripAssignment->cementOrderDetail->destination_id)
            ->value('price') ?? 0;

        // Calculer le montant
        $amount = $tripAssignment->trip->tonnage * $price;

        Log::info('Création de la vente à crédit', [
            'trip_assignment_id' => $tripAssignment->id,
            'quantity' => $tripAssignment->trip->tonnage,
            'price' => $price,
            'amount' => $amount
        ]);

        // Créer la vente à crédit
        $creditSale = CreditSale::create([
            'cement_order_id' => $order->id,
            'cement_order_detail_id' => $tripAssignment->cementOrderDetail->id,
            'trip_assignment_id' => $tripAssignment->id,
            'cashier_id' => auth()->id(),
            'amount' => $amount,
            'amount_paid' => 0,
            'remaining_amount' => $amount,
            'due_date' => $dueDate ?? now()->addDays(30),
            'status' => 'pending'
        ]);

        // Mettre à jour le statut du voyage
        $tripAssignment->update(['status' => 'converted_to_sale']);

        return $creditSale;
    }

    /**
     * Convertir une affectation en vente à crédit
     */
    public function convertAssignment(TripAssignment $assignment)
    {
        DB::beginTransaction();
        try {
            // Vérifier si l'affectation n'est pas déjà convertie
            if ($assignment->status === 'converted_to_sale') {
                throw new \Exception('Cette affectation a déjà été convertie en vente.');
            }

            // Charger les relations nécessaires
            $assignment->load(['cementOrderDetail.order', 'trip']);
            
            // Récupérer le détail de la commande et la commande principale
            $detail = $assignment->cementOrderDetail;
            if (!$detail) {
                throw new \Exception('Détail de commande non trouvé.');
            }

            $order = $detail->order;
            if (!$order) {
                throw new \Exception('Commande principale non trouvée.');
            }

            // Vérifier que nous avons toutes les données nécessaires
            if (!$order->product_id) {
                throw new \Exception('ID du produit manquant dans la commande.');
            }
            if (!$detail->destination_id) {
                throw new \Exception('ID de la ville manquant dans le détail.');
            }

            // Récupérer le produit et vérifier la quantité disponible
            $product = Product::findOrFail($order->product_id);
            
            Log::info('Informations du produit', [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'stock_quantity' => $product->stock_quantity
            ]);

            $tonnage = $detail->tonnage_per_trip ?? $detail->capacity;
            if (!$tonnage) {
                throw new \Exception('Tonnage par voyage non défini.');
            }

            Log::info('Vérification du stock', [
                'tonnage_requis' => $tonnage,
                'stock_disponible' => $product->stock_quantity,
                'suffisant' => $product->stock_quantity >= $tonnage ? 'Oui' : 'Non'
            ]);

            // Vérifier si nous avons assez de stock
            if ($product->stock_quantity < $tonnage) {
                throw new \Exception("Stock insuffisant. Quantité disponible : {$product->stock_quantity}, Quantité requise : {$tonnage}");
            }

            Log::info('Recherche du prix pour la conversion', [
                'detail_id' => $detail->id,
                'product_id' => $order->product_id,
                'destination_id' => $detail->destination_id,
                'cement_order_id' => $detail->cement_order_id
            ]);

            // Récupérer le prix du produit pour cette ville
            $price = ProductPrice::where('product_id', $order->product_id)
                ->where('destination_id', $detail->destination_id)
                ->first();

            if (!$price) {
                throw new \Exception('Prix non trouvé pour ce produit dans cette ville.');
            }

            Log::info('Prix trouvé', [
                'price_id' => $price->id,
                'price_amount' => $price->price
            ]);

            $amount = $price->price * $tonnage;

            Log::info('Calcul du montant', [
                'tonnage' => $tonnage,
                'price' => $price->price,
                'amount' => $amount
            ]);

            // Vérifier que le montant est valide
            if ($amount <= 0) {
                throw new \Exception('Le montant calculé est invalide.');
            }

            // Créer la vente à crédit
            try {
                Log::info('Début de la création de la vente à crédit', [
                    'cement_order_id' => $detail->cement_order_id,
                    'cement_order_detail_id' => $detail->id,
                    'trip_assignment_id' => $assignment->id,
                    'cashier_id' => auth()->id(),
                    'amount' => $amount
                ]);

                // Mettre à jour la quantité du produit
                $product->decrement('stock_quantity', $tonnage);
                Log::info('Quantité du produit mise à jour', [
                    'ancien_stock' => $product->stock_quantity + $tonnage,
                    'nouveau_stock' => $product->stock_quantity
                ]);
                
                // Mettre à jour les quantités du détail de la commande
                $detail->increment('delivered_quantity', $tonnage);
                $detail->decrement('remaining_quantity', $tonnage);
                Log::info('Quantités du détail mises à jour', [
                    'delivered_quantity' => $detail->delivered_quantity,
                    'remaining_quantity' => $detail->remaining_quantity
                ]);

                $creditSale = CreditSale::create([
                    'cement_order_id' => $detail->cement_order_id,
                    'cement_order_detail_id' => $detail->id,
                    'trip_assignment_id' => $assignment->id,
                    'cashier_id' => auth()->id(),
                    'amount' => $amount,
                    'paid_amount' => 0,
                    'remaining_amount' => $amount,
                    'status' => 'pending',
                    'due_date' => now()->addDays(30)
                ]);

                Log::info('Vente à crédit créée', [
                    'credit_sale_id' => $creditSale->id,
                    'amount' => $creditSale->amount,
                    'due_date' => $creditSale->due_date
                ]);

                // Vérifier que la vente à crédit a bien été créée
                if (!$creditSale || !$creditSale->exists) {
                    throw new \Exception('Erreur lors de la création de la vente à crédit.');
                }

                // Mettre à jour le statut de l'affectation
                $assignment->update([
                    'status' => 'converted_to_sale'
                ]);
                Log::info('Statut de l\'affectation mis à jour', [
                    'assignment_id' => $assignment->id,
                    'nouveau_statut' => 'converted_to_sale'
                ]);

                // Mettre à jour le statut du détail si toute la quantité a été livrée
                if ($detail->delivered_quantity >= $detail->quantity) {
                    $detail->update(['status' => 'completed']);
                    Log::info('Statut du détail mis à jour à completed', [
                        'detail_id' => $detail->id,
                        'delivered_quantity' => $detail->delivered_quantity,
                        'quantity_totale' => $detail->quantity
                    ]);
                }

                DB::commit();
                Log::info('Transaction validée avec succès');

                session()->flash('success', 'L\'affectation a été convertie en vente à crédit avec succès.');

                return response()->json([
                    'success' => true,
                    'message' => 'Affectation convertie avec succès'
                ]);

            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Erreur lors de la création de la vente à crédit : ' . $e->getMessage());
                Log::error('Données de la tentative de création :', [
                    'cement_order_id' => $detail->cement_order_id,
                    'cement_order_detail_id' => $detail->id,
                    'trip_assignment_id' => $assignment->id,
                    'cashier_id' => auth()->id(),
                    'amount' => $amount,
                    'status' => 'pending'
                ]);
                throw $e;
            }

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de la conversion de l\'affectation : ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            
            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors de la conversion : ' . $e->getMessage()
            ], 500);
        }
    }

    public function recordPayment(Request $request, CementOrder $order)
    {
        try {
            DB::beginTransaction();

            $request->validate([
                'detail_id' => 'required|exists:cement_order_details,id',
                'trip_assignment_id' => 'nullable|exists:trip_assignments,id',
                'amount' => 'required|numeric|min:0',
                'payment_type' => 'required|in:full,partial',
                'payment_method' => 'required|in:cash,bank_transfer,check',
                'reference_number' => 'required_if:payment_method,bank_transfer,check',
                'bank_name' => 'required_if:payment_method,bank_transfer,check',
                'notes' => 'nullable|string'
            ]);

            // Vérifier que le montant ne dépasse pas le reste à payer
            $detail = $order->details()->findOrFail($request->detail_id);
            $totalPaid = $detail->payments()->sum('amount');
            $remainingAmount = $detail->total_price - $totalPaid;

            if ($request->amount > $remainingAmount) {
                throw new \Exception("Le montant saisi ({$request->amount}) dépasse le montant restant à payer ({$remainingAmount})");
            }

            // Créer le paiement
            $payment = CementOrderPayment::create([
                'cement_order_id' => $order->id,
                'cement_order_detail_id' => $request->detail_id,
                'trip_assignment_id' => $request->trip_assignment_id ?? null,
                'cashier_id' => auth()->id(),
                'amount' => $request->amount,
                'payment_type' => $request->payment_type,
                'payment_method' => $request->payment_method,
                'reference_number' => $request->reference_number ?? null,
                'bank_name' => $request->bank_name ?? null,
                'notes' => $request->notes
            ]);

            // Si c'est un paiement total ou si le montant couvre le reste
            if ($request->payment_type === 'full' || $request->amount >= $remainingAmount) {
                // Marquer tous les voyages comme payés
                if ($request->trip_assignment_id) {
                    TripAssignment::where('id', $request->trip_assignment_id)
                        ->update(['status' => 'converted_to_sale']);
                } else {
                    $detail->tripAssignments()
                        ->where('status', 'validated')
                        ->update(['status' => 'converted_to_sale']);
                }
            }

            DB::commit();

            Log::info('Paiement enregistré', [
                'payment_id' => $payment->id,
                'order_id' => $order->id,
                'detail_id' => $request->detail_id,
                'amount' => $request->amount,
                'payment_type' => $request->payment_type,
                'payment_method' => $request->payment_method
            ]);

            return back()->with('success', 'Le paiement a été enregistré avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de l\'enregistrement du paiement : ' . $e->getMessage());
            return back()->with('error', 'Erreur lors de l\'enregistrement du paiement : ' . $e->getMessage());
        }
    }

    public function getPaymentSummary(CementOrder $order, CementOrderDetail $detail)
    {
        try {
            $detail->load(['tripAssignments.trip', 'product', 'destination']);

            // Récupérer le prix du produit pour cette ville
            $price = ProductPrice::where('product_id', $detail->product_id)
                ->where('destination_id', $detail->destination_id)
                ->value('price') ?? 0;

            // Calculer le montant total
            $totalAmount = $price * $detail->tonnage;

            // Calculer le montant déjà payé
            $paidAmount = $detail->payments()->sum('amount');

            // Calculer le montant restant
            $remainingAmount = max(0, $totalAmount - $paidAmount);

            // Récupérer les voyages non convertis
            $unconvertedTrips = $detail->tripAssignments()
                ->where('status', 'validated')
                ->with(['trip.truck', 'trip.driver'])
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'total_amount' => $totalAmount,
                    'paid_amount' => $paidAmount,
                    'remaining_amount' => $remainingAmount,
                    'unconverted_trips' => $unconvertedTrips
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération du résumé de paiement : ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération du résumé de paiement : ' . $e->getMessage()
            ], 500);
        }
    }

    public function create()
    {
        try {
            // Récupérer la catégorie "ciment"
            $cementCategory = Category::where('name', 'LIKE', '%ciment%')
                ->orWhere('slug', 'LIKE', '%ciment%')
                ->first();

            if (!$cementCategory) {
                Log::error('Catégorie ciment non trouvée');
                return back()->with('error', 'La catégorie ciment n\'est pas configurée dans le système.');
            }

            // Récupérer uniquement les produits de type ciment
            $products = Product::where('category_id', $cementCategory->id)
                ->where('is_active', true)
                ->get();

            if ($products->isEmpty()) {
                Log::error('Aucun produit de type ciment trouvé');
                return back()->with('error', 'Aucun produit de type ciment n\'est disponible.');
            }

            // Récupérer les clients actifs
            $customers = User::role('customer')
                ->where('is_active', true)
                ->get();

            // Récupérer toutes les villes
            $destinations = City::with('region')->get();

            // Récupérer les capacités de camion actives
            $truckCapacities = TruckCapacity::where('is_active', true)
                ->get();

            Log::info('Données chargées pour la vue', [
                'cement_category_id' => $cementCategory->id,
                'products_count' => $products->count(),
                'customers_count' => $customers->count(),
                'destinations_count' => $destinations->count(),
                'truck_capacities_count' => $truckCapacities->count()
            ]);

            return view('cashier.cement-orders.create', compact(
                'products',
                'customers',
                'destinations',
                'truckCapacities'
            ));

        } catch (\Exception $e) {
            Log::error('Erreur dans CementOrderController@create', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return back()->with('error', 'Une erreur est survenue lors du chargement du formulaire.');
        }
    }

    public function store(Request $request)
    {
        DB::beginTransaction();
        try {
            // Validation des données
            $validated = $request->validate([
                'details' => 'required|array|min:1',
                'details.*.product_id' => 'required|exists:products,id',
                'details.*.customer_id' => 'required|exists:users,id',
                'details.*.destination_id' => 'required|exists:cities,id',
                'details.*.quantity' => 'required|numeric|min:0',
                'details.*.trips_count' => 'required|integer|min:1',
                'details.*.tonnage_per_trip' => 'required|numeric|min:0',
                'details.*.capacity_id' => 'required|exists:truck_capacities,id'
            ]);

            // Génération de la référence
            $reference = 'CMD-' . date('YmdHis') . '-' . auth()->id();

            // Création du bon de commande
            $order = CementOrder::create([
                'reference' => $reference,
                'product_id' => $validated['details'][0]['product_id'], // Premier produit
                'created_by' => auth()->id(),
                'status' => 'pending'
            ]);

            // Création des détails
            foreach ($validated['details'] as $detail) {
                // Récupération du prix unitaire pour la ville
                $price = ProductPrice::where('product_id', $detail['product_id'])
                    ->where('destination_id', $detail['destination_id'])
                    ->value('price') ?? 0;

                CementOrderDetail::create([
                    'cement_order_id' => $order->id,
                    'product_id' => $detail['product_id'],
                    'customer_id' => $detail['customer_id'],
                    'destination_id' => $detail['destination_id'],
                    'quantity' => $detail['quantity'],
                    'unit_price' => $price,
                    'total_price' => $price * $detail['quantity'],
                    'trips_count' => $detail['trips_count'],
                    'tonnage_per_trip' => $detail['tonnage_per_trip'],
                    'capacity' => $detail['tonnage_per_trip'],
                    'delivered_quantity' => 0,
                    'remaining_quantity' => $detail['quantity'],
                    'status' => 'pending'
                ]);
            }

            // Mise à jour des montants du bon de commande
            $totalAmount = $order->details()->sum('total_price');
            $order->update([
                'total_amount' => $totalAmount,
                'paid_amount' => 0,
                'remaining_amount' => $totalAmount
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Bon de commande créé avec succès',
                'data' => [
                    'id' => $order->id,
                    'reference' => $order->reference
                ]
            ]);

        } catch (ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Erreur de validation',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de la création du bon de commande', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors de la création du bon de commande'
            ], 500);
        }
    }
}
