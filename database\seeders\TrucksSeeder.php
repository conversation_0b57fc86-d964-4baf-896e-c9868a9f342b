<?php

namespace Database\Seeders;

use App\Models\Truck;
use App\Models\Driver;
use App\Models\TruckCapacity;
use Illuminate\Database\Seeder;

class TrucksSeeder extends Seeder
{
    public function run()
    {
        // Récupérer toutes les capacités
        $capacities = TruckCapacity::all();
        $drivers = Driver::all();
        
        if ($capacities->isEmpty()) {
            throw new \Exception('Aucune capacité de camion trouvée. Veuillez d\'abord exécuter TruckCapacitySeeder.');
        }

        if ($drivers->isEmpty()) {
            throw new \Exception('Aucun chauffeur trouvé. Veuillez d\'abord exécuter DriversSeeder.');
        }

        $trucks = [
            [
                'registration_number' => 'TG-1234-AA',
                'brand' => 'Volvo',
                'model' => 'FH16',
                'year' => 2022,
                'status' => 'assigned',
                'truck_capacity_id' => $capacities->random()->id,
                'driver_id' => $drivers->random()->id,
                'notes' => 'Premier camion'
            ],
            [
                'registration_number' => 'TG-5678-AB',
                'brand' => 'Mercedes',
                'model' => 'Actros',
                'year' => 2023,
                'status' => 'assigned',
                'truck_capacity_id' => $capacities->random()->id,
                'driver_id' => $drivers->random()->id,
                'notes' => 'Deuxième camion'
            ],
            [
                'registration_number' => 'TG-9012-AC',
                'brand' => 'MAN',
                'model' => 'TGX',
                'year' => 2023,
                'status' => 'assigned',
                'truck_capacity_id' => $capacities->random()->id,
                'driver_id' => $drivers->random()->id,
                'notes' => 'Troisième camion'
            ]
        ];

        foreach ($trucks as $truck) {
            Truck::create($truck);
        }
    }
}
