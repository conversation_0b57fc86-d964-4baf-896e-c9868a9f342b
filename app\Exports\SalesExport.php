<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class SalesExport implements FromCollection, WithHeadings, WithStyles, WithTitle
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        $collection = collect();
        
        // Statistiques
        $collection->push([
            'STATISTIQUES GÉNÉRALES', '', '', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Total des ventes', number_format($this->data['stats']['total_sales']), '', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Montant total', number_format($this->data['stats']['total_amount'], 0, ',', ' ') . ' FCFA', '', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Montant payé', number_format($this->data['stats']['total_paid'], 0, ',', ' ') . ' FCFA', '', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Montant en attente', number_format($this->data['stats']['total_pending'], 0, ',', ' ') . ' FCFA', '', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Vente moyenne', number_format($this->data['stats']['average_sale'], 0, ',', ' ') . ' FCFA', '', '', '', '', '', '', ''
        ]);
        $collection->push(['', '', '', '', '', '', '', '', '']); // Ligne vide

        // En-têtes des ventes
        $collection->push([
            'LISTE DES VENTES', '', '', '', '', '', '', '', ''
        ]);
        $collection->push([
            'Date', 'Référence', 'Client', 'Téléphone', 'Produit', 'Quantité', 'Montant (FCFA)', 'Statut Paiement', 'Statut Livraison'
        ]);

        // Données des ventes
        foreach ($this->data['sales'] as $sale) {
            $collection->push([
                $sale->created_at->format('d/m/Y H:i'),
                $sale->invoice_number ?? 'N/A',
                $sale->customer_name ?? 'N/A',
                $sale->customer_phone ?? 'N/A',
                $sale->supply ? $sale->supply->reference : 'N/A',
                number_format($sale->quantity, 2),
                number_format($sale->total_amount, 0, ',', ' '),
                $this->getPaymentStatusText($sale->payment_status),
                $this->getDeliveryStatusText($sale->delivery_status)
            ]);
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'RAPPORT DES VENTES',
            'Période: ' . \Carbon\Carbon::parse($this->data['start_date'])->format('d/m/Y') . ' - ' . \Carbon\Carbon::parse($this->data['end_date'])->format('d/m/Y'),
            '',
            '',
            '',
            '',
            '',
            '',
            ''
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 16,
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => 'FF36B9CC',
                    ],
                ],
            ],
            2 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],
        ];
    }

    public function title(): string
    {
        return 'Ventes';
    }

    private function getPaymentStatusText($status)
    {
        switch ($status) {
            case 'paid':
                return 'Payé';
            case 'pending':
                return 'En attente';
            case 'cancelled':
                return 'Annulé';
            default:
                return $status;
        }
    }

    private function getDeliveryStatusText($status)
    {
        switch ($status) {
            case 'delivered':
                return 'Livré';
            case 'in_progress':
                return 'En cours';
            case 'pending':
                return 'En attente';
            default:
                return $status;
        }
    }
}
