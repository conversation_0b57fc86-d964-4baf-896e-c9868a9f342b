<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Category;
use App\Models\ProductPrice;
use App\Models\IronSpecification;
use App\Models\OrderItem;
use App\Models\CementOrder;
use App\Models\SupplyDetail;
use App\Models\Sale;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
        'slug',
        'description',
        'category_id',
        'unit',
        'stock_quantity',
        'price',
        'is_active'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'stock_quantity' => 'integer',
        'price' => 'float',
        'is_active' => 'boolean'
    ];

    protected static function boot()
    {
        parent::boot();

        // Avant de supprimer un produit
        static::deleting(function ($product) {
            // Supprimer les prix associés
            $product->prices()->delete();
            
            // Supprimer les spécifications de fer
            if ($product->ironSpecification) {
                $product->ironSpecification->delete();
            }
            
            // Supprimer les détails d'approvisionnement
            $product->supplyDetails()->delete();
        });
    }

    // Relation avec la catégorie
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function cementOrders(): HasMany
    {
        return $this->hasMany(CementOrder::class);
    }

    public function prices(): HasMany
    {
        return $this->hasMany(ProductPrice::class);
    }

    public function ironSpecification(): HasOne
    {
        return $this->hasOne(IronSpecification::class);
    }

    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    public function supplyDetails(): HasMany
    {
        return $this->hasMany(SupplyDetail::class);
    }

    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    public function getPriceForCity($cityId)
    {
        return $this->prices()->where('city_id', $cityId)->first()?->price ?? $this->price;
    }
}
