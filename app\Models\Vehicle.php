<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Vehicle extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'registration_number',
        'brand',
        'model',
        'year',
        'capacity',
        'status',
        'last_maintenance',
        'next_maintenance',
        'driver_id',
        'notes'
    ];

    protected $casts = [
        'last_maintenance' => 'datetime',
        'next_maintenance' => 'datetime',
        'year' => 'integer'
    ];

    public function driver()
    {
        return $this->belongsTo(Driver::class);
    }

    public function getFullInfoAttribute()
    {
        return "{$this->brand} {$this->model} ({$this->registration_number})";
    }
}
