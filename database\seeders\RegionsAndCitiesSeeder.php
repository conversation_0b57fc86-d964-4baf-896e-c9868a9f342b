<?php

namespace Database\Seeders;

use App\Models\Region;
use App\Models\City;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class RegionsAndCitiesSeeder extends Seeder
{
    public function run(): void
    {
        // Désactivation des contraintes de clé étrangère
        \DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Suppression des données existantes
        \DB::table('cities')->delete();
        \DB::table('regions')->delete();

        // Réinitialisation des séquences d'ID
        \DB::statement('ALTER TABLE cities AUTO_INCREMENT = 1');
        \DB::statement('ALTER TABLE regions AUTO_INCREMENT = 1');

        // Réactivation des contraintes de clé étrangère
        \DB::statement('SET FOREIGN_KEY_CHECKS=1');

        $regions = [
            'Maritime' => [
                // Préfecture du Golfe
                'Lomé', 'Baguida', 'Legbassi<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'Vakpossito', 'Sang<PERSON>ra',
                // Préfecture de Lacs
                'Aného', 'Agbodrafo', 'Aklakou', 'Gbodjo<PERSON>', 'Séwatsrikopé', 'Agouègan', 'Anfoin',
                // Préfecture de Vo
                'Vogan', 'Togoville', 'Dagbati', 'Vo-Koutimé', 'Zalivé', 'Zafi',
                // Préfecture de Yoto
                'Tabligbo', 'Gboto', 'Kouvé', 'Tchékpo', 'Ahépé', 'Essé-Godjin',
                // Préfecture de Zio
                'Tsévié', 'Davié', 'Gbatopé', 'Agbélouvé', 'Dalavé', 'Mission-Tové',
                // Préfecture de Bas-Mono
                'Afagnagan', 'Attitogon', 'Agbétiko'
            ],
            'Plateaux' => [
                // Préfecture d'Agou
                'Agou-Gadzépé', 'Agou-Nyogbo', 'Agou-Gadja', 'Agou-Iboe', 'Agou-Kébo',
                // Préfecture de Kloto
                'Kpalimé', 'Kpadapé', 'Tové', 'Agomé-Tomégbé', 'Kuma',
                // Préfecture de Danyi
                'Danyi-Apéyémé', 'Danyi-Elavagnon', 'Danyi-Kakpa', 'Danyi-Dzogbégan',
                // Préfecture d'Amou
                'Amlamé', 'Imlé', 'Otadi', 'Oblo', 'Wokpo',
                // Préfecture de Wawa
                'Badou', 'Tomégbé', 'Kpété-Béna', "Gbadi-N'Kugna",
                // Préfecture de l'Est-Mono
                'Elavagnon', 'Morita', 'Ké-Mono', 'Gbébé',
                // Préfecture de Haho
                'Notsé', 'Wahala', 'Kpédomé', 'Asrama', 'Hahomégbé',
                // Préfecture du Moyen-Mono
                'Tohoun', 'Tado', 'Kpékplémé', 'Djindjinopé',
                // Préfecture d'Akébou
                'Kougnohou', 'Yégué', 'Sérégbéné', 'Kessibo-Wawa',
                // Préfecture de l'Ogou
                'Atakpamé', 'Datcha', 'Glei', 'Akparé', 'Kamina', 'Igboloudja', 'Kolokopé', 'Doumé', 'Gnagna', 'Akaba',
                // Préfecture d'Anié
                'Anié', 'Pallakoko', 'Glitto', 'Adogbénou'
            ],
            'Centrale' => [
                // Préfecture de Tchaoudjo
                'Sokodé', 'Komah', 'Kpangalam', 'Tchalo', 'Kédia', 'Lama-Téssi',
                // Préfecture de Tchamba
                'Tchamba', 'Alibi', 'Koussountou', 'Balanka', 'Affem-Kabyè',
                // Préfecture de Sotouboua
                'Sotouboua', 'Adjengré', 'Tchébébé', 'Fazao', 'Titigbé',
                // Préfecture de Blitta
                'Blitta', 'Blitta-Gare', 'Pagala', 'Welly', 'Yaloumbé',
                // Préfecture de Mo
                'Djarkpanga', 'Soudou', 'Kolina', 'Boulohou'
            ],
            'Kara' => [
                // Préfecture de la Kozah
                'Kara', 'Pya', 'Tcharé', 'Awandjélo', 'Lassa',
                // Préfecture de Bassar
                'Bassar', 'Kabou', 'Bangéli', 'Bitchabé', 'Sanda-Afohou',
                // Préfecture de la Binah
                'Pagouda', 'Kétao', 'Pessaré', 'Sirka', 'Solla',
                // Préfecture de Dankpen
                'Guérin-Kouka', 'Namon', 'Koutchichéou', 'Bapuré',
                // Préfecture de Doufelgou
                'Niamtougou', 'Siou', 'Massédéna', 'Alloum',
                // Préfecture de Kéran
                'Kandé', 'Nadoba', 'Warengo', 'Ataloté',
                // Préfecture d'Assoli
                'Bafilo', 'Dako', 'Koumondé'
            ],
            'Savanes' => [
                // Préfecture de Tone
                'Dapaong', 'Warkambou', 'Korbongou', 'Pana', 'Lotogou',
                // Préfecture de l'Oti
                'Mango', 'Mogou', 'Tchanaga', 'Koumongou',
                // Préfecture de Tandjoaré
                'Tandjoaré', 'Nano', 'Tampialim', 'Bogou', 'Naki-Est',
                // Préfecture de Kpendjal
                'Mandouri', 'Borgou', 'Naki-Ouest', 'Ogaro', 'Namoundjoga',
                // Préfecture de Cinkassé
                'Cinkassé', 'Timbou', 'Biankouri', 'Gouloungoussi',
                // Préfecture de l'Oti-Sud
                'Gando', 'Sagbiebou'
            ]
        ];

        foreach ($regions as $regionName => $cities) {
            $region = Region::create([
                'name' => $regionName,
                'slug' => Str::slug($regionName)
            ]);

            foreach ($cities as $cityName) {
                City::create([
                    'region_id' => $region->id,
                    'name' => $cityName,
                    'slug' => Str::slug($cityName)
                ]);
            }
        }
    }
}
