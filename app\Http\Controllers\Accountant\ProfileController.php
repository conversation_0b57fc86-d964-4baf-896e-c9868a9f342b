<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Password;

class ProfileController extends Controller
{
    public function show()
    {
        return view('accountant.profile.show', [
            'user' => Auth::user()
        ]);
    }

    public function edit()
    {
        return view('accountant.profile.edit', [
            'user' => Auth::user()
        ]);
    }

    public function update(Request $request)
    {
        $user = Auth::user();

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'phone' => ['nullable', 'string', 'max:20'],
            'position' => ['nullable', 'string', 'max:100'],
            'avatar' => ['nullable', 'image', 'max:1024'], // Max 1MB
        ]);

        if ($request->hasFile('avatar')) {
            // Déterminer le type d'utilisateur pour le dossier de stockage
            $userType = 'accountant';
            $uploadPath = 'uploads/avatars/' . $userType;
            
            // Créer le dossier s'il n'existe pas (par sécurité)
            if (!file_exists(public_path($uploadPath))) {
                mkdir(public_path($uploadPath), 0755, true);
            }
            
            // Compter le nombre d'avatars existants pour cet utilisateur pour déterminer le rang
            $rank = 1;
            $nameSlug = Str::slug($user->name); // Convertir le nom en slug (sans espaces ni caractères spéciaux)
            $pattern = public_path($uploadPath . '/' . $userType . '_' . $nameSlug . '_*');
            $existingFiles = glob($pattern);
            if (!empty($existingFiles)) {
                $rank = count($existingFiles) + 1;
            }
            
            // Générer un nom de fichier selon le format demandé: type_nom_date_rang
            $dateFormatted = date('Ymd');
            $filename = $userType . '_' . $nameSlug . '_' . $dateFormatted . '_' . $rank . '.' . $request->file('avatar')->getClientOriginalExtension();
            
            // Supprimer l'ancienne image si elle existe
            if ($user->avatar) {
                // Vérifier si l'avatar est dans l'ancien chemin (storage/app/public/avatars)
                if (strpos($user->avatar, 'avatars/') === 0) {
                    Storage::disk('public')->delete($user->avatar);
                } else {
                    // Sinon, c'est dans le nouveau chemin (public/uploads/avatars/usertype)
                    if (file_exists(public_path($user->avatar))) {
                        unlink(public_path($user->avatar));
                    }
                }
            }
            
            // Déplacer le fichier vers le dossier approprié
            $request->file('avatar')->move(public_path($uploadPath), $filename);
            
            // Enregistrer le chemin relatif dans la base de données
            $user->avatar = $uploadPath . '/' . $filename;
        }

        $user->name = $validated['name'];
        $user->email = $validated['email'];
        
        // Les colonnes phone et position existent maintenant dans la base de données
        if (isset($validated['phone'])) {
            $user->phone = $validated['phone'];
        }
        
        if (isset($validated['position'])) {
            $user->position = $validated['position'];
        }
        
        $user->save();

        return redirect()->route('accountant.profile.show')
            ->with('success', 'Profil mis à jour avec succès.');
    }

    public function showPasswordForm()
    {
        return view('accountant.profile.password');
    }

    public function updatePassword(Request $request)
    {
        $validated = $request->validate([
            'current_password' => ['required', 'current_password'],
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        Auth::user()->update([
            'password' => Hash::make($validated['password']),
        ]);

        return redirect()->route('accountant.profile.show')
            ->with('success', 'Mot de passe mis à jour avec succès.');
    }
}
