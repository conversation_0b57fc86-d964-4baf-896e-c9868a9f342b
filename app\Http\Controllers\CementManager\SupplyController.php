<?php

namespace App\Http\Controllers\CementManager;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Supply;

class SupplyController extends Controller
{
    public function index()
    {
        $supplies = Supply::latest()->paginate(10);
        return view('cement-manager.supplies.index', compact('supplies'));
    }

    public function show(Supply $supply)
    {
        $supply->load([
            'details.product',
            'cities' => function($query) {
                $query->with(['city', 'vehicle', 'driver']);
            }
        ]);
        
        return view('cement-manager.supplies.show', compact('supply'));
    }
}
