<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Destination;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;

class DestinationController extends Controller
{
    public function store(Request $request)
    {
        Log::info('Tentative de création d\'une destination', $request->all());

        // Validation avec messages personnalisés
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:destinations,name',
            'type' => 'required|in:client,partner'
        ], [
            'name.required' => 'Le nom est obligatoire',
            'name.unique' => 'Cette destination existe déjà',
            'type.required' => 'Le type est obligatoire',
            'type.in' => 'Le type doit être client ou partenaire'
        ]);

        if ($validator->fails()) {
            Log::warning('Validation échouée pour la création de destination', [
                'errors' => $validator->errors()->toArray()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur de validation',
                'errors' => $validator->errors()
            ], 422);
        }

        DB::beginTransaction();

        try {
            // Vérifier si la table existe
            if (!Schema::hasTable('destinations')) {
                throw new \Exception('La table destinations n\'existe pas');
            }

            // Vérifier les colonnes requises
            $requiredColumns = ['name', 'type', 'is_active', 'created_by'];
            $missingColumns = [];
            foreach ($requiredColumns as $column) {
                if (!Schema::hasColumn('destinations', $column)) {
                    $missingColumns[] = $column;
                }
            }

            if (!empty($missingColumns)) {
                throw new \Exception('Colonnes manquantes dans la table destinations: ' . implode(', ', $missingColumns));
            }

            $destination = Destination::create([
                'name' => $request->name,
                'type' => $request->type,
                'is_active' => true,
                'created_by' => Auth::id()
            ]);

            DB::commit();

            Log::info('Nouvelle destination créée avec succès', [
                'id' => $destination->id,
                'name' => $destination->name,
                'type' => $destination->type,
                'created_by' => Auth::id()
            ]);

            return response()->json([
                'success' => true,
                'message' => $destination->type === 'client' ? 'Client ajouté avec succès' : 'Partenaire ajouté avec succès',
                'data' => $destination
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Erreur lors de la création d\'une destination', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors de l\'ajout: ' . $e->getMessage()
            ], 500);
        }
    }

    public function index()
    {
        try {
            $destinations = Destination::where('is_active', true)
                ->whereIn('type', ['client', 'partner'])
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $destinations
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des destinations', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors de la récupération des destinations'
            ], 500);
        }
    }
}
