<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Delivery extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'reference',
        'driver_id',
        'customer_id',
        'status',
        'expected_delivery_date',
        'actual_delivery_date',
        'notes'
    ];

    protected $casts = [
        'expected_delivery_date' => 'datetime',
        'actual_delivery_date' => 'datetime'
    ];

    /**
     * Get the driver that handles the delivery.
     */
    public function driver()
    {
        return $this->belongsTo(Driver::class);
    }

    /**
     * Get the customer for this delivery.
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the products for this delivery.
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, 'delivery_products')
                    ->withPivot('quantity')
                    ->withTimestamps();
    }
}
