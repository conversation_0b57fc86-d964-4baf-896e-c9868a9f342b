<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Création ou mise à jour des rôles
        $roles = [
            'admin',
            'accountant',
            'cement_manager',
            'iron_manager',
            'cashier',
            'customer_service',
            'customer',
            'driver'
        ];

        foreach ($roles as $role) {
            Role::updateOrCreate(['name' => $role]);
        }
    }
}
