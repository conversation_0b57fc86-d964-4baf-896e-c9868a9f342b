<?php

namespace App\Http\Controllers\CementManager;

use App\Http\Controllers\Controller;
use App\Models\CementOrder;
use Illuminate\Http\Request;

class CementOrderController extends Controller
{
    public function index()
    {
        $orders = CementOrder::with(['details.product', 'details.destination', 'details.customer'])
            ->latest()
            ->paginate(10);

        $stats = [
            'total' => CementOrder::count(),
            'pending' => CementOrder::where('status', 'pending')->count(),
            'approved' => CementOrder::where('status', 'approved')->count(),
            'rejected' => CementOrder::where('status', 'rejected')->count(),
            'trips' => 0, // À implémenter selon votre logique de voyages
        ];

        return view('cement-manager.orders.index', compact('orders', 'stats'));
    }

    public function show(CementOrder $cement_order)
    {
        $cement_order->load(['details.product', 'details.destination', 'details.customer']);
        return view('cement-manager.orders.show', compact('cement_order'));
    }
}
