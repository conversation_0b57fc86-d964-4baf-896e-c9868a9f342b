// 🧪 Script de test pour les exports et rapports - À copier dans la console du navigateur
// Ouvrez http://127.0.0.1:8000/accountant/dashboard-professional puis F12 > Console

console.log('🚀 Initialisation des tests d\'export et rapports...');

// Fonction utilitaire pour obtenir le token CSRF
function getCSRFToken() {
    const token = document.querySelector('meta[name="csrf-token"]');
    return token ? token.getAttribute('content') : null;
}

// Fonction pour tester l'export des ventes Excel
async function testExportSalesExcel() {
    console.log('📊 Test: Export Ventes Excel');
    
    try {
        const response = await fetch('/accountant/dashboard/export/sales-excel', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': getCSRFToken()
            },
            body: JSON.stringify({
                start_date: '2024-01-01',
                end_date: '2024-12-31',
                payment_status: 'all'
            })
        });
        
        if (response.ok) {
            const blob = await response.blob();
            console.log('✅ Export Excel réussi!', blob);
            
            // Télécharger le fichier
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'ventes-export-test.xlsx';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            return { success: true, message: 'Export Excel téléchargé avec succès' };
        } else {
            const error = await response.text();
            console.error('❌ Erreur Export Excel:', error);
            return { success: false, message: error };
        }
    } catch (error) {
        console.error('❌ Erreur Export Excel:', error);
        return { success: false, message: error.message };
    }
}

// Fonction pour tester l'export des paiements PDF
async function testExportPaymentsPDF() {
    console.log('💰 Test: Export Paiements PDF');
    
    try {
        const response = await fetch('/accountant/dashboard/export/payments-pdf', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': getCSRFToken()
            },
            body: JSON.stringify({
                start_date: '2024-01-01',
                end_date: '2024-12-31'
            })
        });
        
        const data = await response.json();
        console.log('📄 Réponse Export PDF:', data);
        
        if (data.success) {
            console.log('✅ Export PDF réussi!', data);
            return data;
        } else {
            console.error('❌ Erreur Export PDF:', data.message);
            return data;
        }
    } catch (error) {
        console.error('❌ Erreur Export PDF:', error);
        return { success: false, message: error.message };
    }
}

// Fonction pour tester l'export du tableau de bord PDF
async function testExportDashboardPDF() {
    console.log('📋 Test: Export Tableau de Bord PDF');
    
    try {
        const response = await fetch('/accountant/dashboard/export/dashboard-pdf', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': getCSRFToken()
            },
            body: JSON.stringify({})
        });
        
        const data = await response.json();
        console.log('📊 Réponse Export Dashboard:', data);
        
        if (data.success) {
            console.log('✅ Export Dashboard réussi!', data);
            return data;
        } else {
            console.error('❌ Erreur Export Dashboard:', data.message);
            return data;
        }
    } catch (error) {
        console.error('❌ Erreur Export Dashboard:', error);
        return { success: false, message: error.message };
    }
}

// Fonction pour tester les rapports prédéfinis
async function testGenerateReport(type) {
    console.log(`📑 Test: Rapport ${type}`);
    
    try {
        const response = await fetch('/accountant/dashboard/reports/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': getCSRFToken()
            },
            body: JSON.stringify({ type: type })
        });
        
        const data = await response.json();
        console.log(`📈 Réponse Rapport ${type}:`, data);
        
        if (data.success) {
            console.log(`✅ Rapport ${type} généré!`, data);
            return data;
        } else {
            console.error(`❌ Erreur Rapport ${type}:`, data.message);
            return data;
        }
    } catch (error) {
        console.error(`❌ Erreur Rapport ${type}:`, error);
        return { success: false, message: error.message };
    }
}

// Fonction pour tester tous les exports
async function testAllExports() {
    console.log('🎯 Démarrage de tous les tests...');
    
    const results = {};
    
    // Test Export Excel
    results.excel = await testExportSalesExcel();
    await new Promise(resolve => setTimeout(resolve, 1000)); // Pause 1s
    
    // Test Export PDF Paiements
    results.paymentsPdf = await testExportPaymentsPDF();
    await new Promise(resolve => setTimeout(resolve, 1000)); // Pause 1s
    
    // Test Export PDF Dashboard
    results.dashboardPdf = await testExportDashboardPDF();
    await new Promise(resolve => setTimeout(resolve, 1000)); // Pause 1s
    
    // Test Rapports
    results.weeklyReport = await testGenerateReport('weekly');
    await new Promise(resolve => setTimeout(resolve, 1000)); // Pause 1s
    
    results.monthlyReport = await testGenerateReport('monthly');
    await new Promise(resolve => setTimeout(resolve, 1000)); // Pause 1s
    
    results.performanceReport = await testGenerateReport('performance');
    
    console.log('🏁 Résultats de tous les tests:', results);
    
    // Résumé
    const successCount = Object.values(results).filter(r => r.success).length;
    const totalCount = Object.keys(results).length;
    
    console.log(`📊 Résumé: ${successCount}/${totalCount} tests réussis`);
    
    if (successCount === totalCount) {
        console.log('🎉 Tous les tests sont passés avec succès!');
    } else {
        console.log('⚠️ Certains tests ont échoué. Vérifiez les détails ci-dessus.');
    }
    
    return results;
}

// Fonctions individuelles pour tests rapides
window.testExcel = testExportSalesExcel;
window.testPaymentsPDF = testExportPaymentsPDF;
window.testDashboardPDF = testExportDashboardPDF;
window.testWeeklyReport = () => testGenerateReport('weekly');
window.testMonthlyReport = () => testGenerateReport('monthly');
window.testPerformanceReport = () => testGenerateReport('performance');
window.testAll = testAllExports;

console.log('✅ Tests prêts! Utilisez ces commandes:');
console.log('📊 testExcel() - Test export ventes Excel');
console.log('💰 testPaymentsPDF() - Test export paiements PDF');
console.log('📋 testDashboardPDF() - Test export tableau de bord PDF');
console.log('📅 testWeeklyReport() - Test rapport hebdomadaire');
console.log('📆 testMonthlyReport() - Test rapport mensuel');
console.log('📈 testPerformanceReport() - Test rapport performance');
console.log('🎯 testAll() - Lancer tous les tests');
console.log('');
console.log('💡 Exemple: tapez testAll() puis Entrée pour tester tout');
