<?php

namespace Database\Seeders;

use App\Models\Driver;
use Illuminate\Database\Seeder;

class DriversSeeder extends Seeder
{
    public function run()
    {
        $drivers = [
            [
                'first_name' => '<PERSON>',
                'last_name' => 'Doe',
                'phone' => '+228 90123456',
                'license_number' => 'DL123456',
                'license_expiry' => '2025-12-31',
                'is_active' => true,
                'notes' => 'Experienced driver with clean record'
            ],
            [
                'first_name' => '<PERSON>',
                'last_name' => 'Smith',
                'phone' => '+228 91234567',
                'license_number' => 'DL234567',
                'license_expiry' => '2025-12-31',
                'is_active' => true,
                'notes' => 'Specialized in long-haul trips'
            ],
            [
                'first_name' => 'Bob',
                'last_name' => 'Johnson',
                'phone' => '+228 92345678',
                'license_number' => 'DL345678',
                'license_expiry' => '2025-12-31',
                'is_active' => true,
                'notes' => 'Safety-focused driver'
            ]
        ];

        foreach ($drivers as $driver) {
            Driver::create($driver);
        }
    }
}
