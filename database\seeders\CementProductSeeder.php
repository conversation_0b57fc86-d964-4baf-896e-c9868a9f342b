<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CementProductSeeder extends Seeder
{
    public function run(): void
    {
        // Créer la catégorie Ciment si elle n'existe pas
        $categoryId = DB::table('categories')->insertGetId([
            'name' => 'Ciment',
            'slug' => 'ciment',
            'description' => 'Catégorie des produits ciment',
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Créer le produit Ciment Portland
        DB::table('products')->insert([
            'name' => 'Ciment Portland',
            'slug' => 'ciment-portland',
            'description' => 'Ciment Portland de haute qualité',
            'category_id' => $categoryId,
            'unit' => 'tonne',
            'stock_quantity' => 1000,
            'price' => 75000,
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }
}
