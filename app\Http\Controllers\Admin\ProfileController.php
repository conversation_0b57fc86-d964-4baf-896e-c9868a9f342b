<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules\Password;

class ProfileController extends Controller
{
    public function show()
    {
        return view('admin.profile.show', [
            'user' => Auth::user()
        ]);
    }

    public function edit()
    {
        return view('admin.profile.edit', [
            'user' => Auth::user()
        ]);
    }

    public function update(Request $request)
    {
        // Log des données reçues pour débogage
        \Illuminate\Support\Facades\Log::info('Données reçues pour la mise à jour du profil:', [
            'request_all' => $request->all(),
            'has_avatar' => $request->hasFile('avatar')
        ]);
        
        $user = Auth::user();
        
        // Log des informations utilisateur avant mise à jour
        \Illuminate\Support\Facades\Log::info('Utilisateur avant mise à jour:', [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'phone' => $user->phone,
            'position' => $user->position,
            'avatar' => $user->avatar
        ]);

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'avatar' => ['nullable', 'image', 'max:2048'], // Max 2MB
            'phone' => ['nullable', 'string', 'max:20'],
            'position' => ['nullable', 'string', 'max:100'],
        ]);
        
        // Log des données validées
        \Illuminate\Support\Facades\Log::info('Données validées:', $validated);

        if ($request->hasFile('avatar')) {
            // Créer le dossier s'il n'existe pas
            $uploadPath = 'uploads/avatars/admin';
            if (!file_exists(public_path($uploadPath))) {
                mkdir(public_path($uploadPath), 0777, true);
            }
            
            // Supprimer l'ancienne image si elle existe
            if ($user->avatar && file_exists(public_path($user->avatar))) {
                \Illuminate\Support\Facades\Log::info('Suppression de l\'ancien avatar:', ['path' => public_path($user->avatar)]);
                unlink(public_path($user->avatar));
            }

            // Générer un nom de fichier unique
            $avatarName = 'admin_' . $user->id . '_' . time() . '.' . $request->file('avatar')->getClientOriginalExtension();
            
            // Déplacer le fichier
            $request->file('avatar')->move(public_path($uploadPath), $avatarName);
            
            // Enregistrer le chemin relatif dans la base de données
            $user->avatar = $uploadPath . '/' . $avatarName;
            \Illuminate\Support\Facades\Log::info('Nouvel avatar enregistré:', ['path' => $user->avatar]);
        }

        $user->name = $validated['name'];
        $user->email = $validated['email'];
        $user->phone = $validated['phone'] ?? $user->phone;
        $user->position = $validated['position'] ?? $user->position;
        
        $result = $user->save();
        
        // Log du résultat de la sauvegarde et des informations utilisateur après mise à jour
        \Illuminate\Support\Facades\Log::info('Résultat de la sauvegarde:', [
            'success' => $result,
            'user_after' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'position' => $user->position,
                'avatar' => $user->avatar
            ]
        ]);

        return redirect()->route('admin.profile.show')
            ->with('success', 'Profil mis à jour avec succès.');
    }

    public function showPasswordForm()
    {
        return view('admin.profile.password');
    }

    public function updatePassword(Request $request)
    {
        $validated = $request->validate([
            'current_password' => ['required', 'current_password'],
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        Auth::user()->update([
            'password' => Hash::make($validated['password']),
        ]);

        return redirect()->route('admin.profile.show')
            ->with('success', 'Mot de passe mis à jour avec succès.');
    }
}
