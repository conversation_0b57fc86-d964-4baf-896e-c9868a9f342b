<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('supplies', function (Blueprint $table) {
            // Modify existing columns according to new specification
            $table->string('reference')->change();
            $table->foreignId('supplier_id')->change();
            $table->foreignId('category_id')->change();
            $table->date('date')->change();
            $table->date('expected_delivery_date')->change();
            $table->text('notes')->nullable()->change();
            $table->decimal('total_tonnage', 10, 2)->change();
            $table->decimal('total_amount', 10, 2)->change();
            $table->decimal('total_remaining', 10, 2)->change();
            $table->string('status')->change();
            $table->text('rejection_reason')->nullable()->change();
            $table->foreignId('created_by')->change();
        });
    }

    public function down()
    {
        Schema::table('supplies', function (Blueprint $table) {
            // Revert column modifications
            $table->string('reference')->unique()->change();
            $table->foreignId('supplier_id')->constrained('suppliers')->change();
            $table->foreignId('category_id')->constrained('categories')->change();
            $table->date('date')->change();
            $table->date('expected_delivery_date')->change();
            $table->text('notes')->nullable()->change();
            $table->decimal('total_tonnage', 10, 2)->default(0)->change();
            $table->decimal('total_amount', 12, 2)->default(0)->change();
            $table->decimal('total_remaining', 12, 2)->default(0)->change();
            $table->enum('status', ['pending', 'partial', 'completed', 'cancelled'])->default('pending')->change();
            $table->string('rejection_reason')->nullable()->change();
            $table->foreignId('created_by')->constrained('users')->change();
        });
    }
};
