<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('sales', function (Blueprint $table) {
            if (!Schema::hasColumn('sales', 'discount_per_ton')) {
                $table->decimal('discount_per_ton', 12, 2)->nullable()->after('unit_price');
            }
            
            if (!Schema::hasColumn('sales', 'discount_total')) {
                $table->decimal('discount_total', 12, 2)->nullable()->after('discount_per_ton');
            }
            
            if (!Schema::hasColumn('sales', 'total_before_discount')) {
                $table->decimal('total_before_discount', 12, 2)->nullable()->after('unit_price');
            }
            
            if (!Schema::hasColumn('sales', 'admin_validation_status')) {
                $table->string('admin_validation_status')->default('not_required')->after('status');
            }
        });
    }

    public function down()
    {
        Schema::table('sales', function (Blueprint $table) {
            $table->dropColumn(['discount_per_ton', 'discount_total', 'total_before_discount', 'admin_validation_status']);
        });
    }
};
