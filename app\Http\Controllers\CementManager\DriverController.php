<?php

namespace App\Http\Controllers\CementManager;

use App\Http\Controllers\Controller;
use App\Models\Driver;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class DriverController extends Controller
{
    public function index()
    {
        $drivers = Driver::orderBy('created_at', 'desc')->paginate(10);
        return view('cement-manager.drivers.index', compact('drivers'));
    }

    public function create()
    {
        return view('cement-manager.drivers.create');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone_number' => 'required|string|max:20|unique:drivers,phone_number',
            'license_number' => 'required|string|max:50|unique:drivers,license_number',
            'license_expiry' => 'required|date|after:today',
            'address' => 'required|string|max:500',
            'emergency_phone' => 'required|string|max:20',
            'is_available' => 'required|boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        DB::beginTransaction();
        try {
            $data = $request->all();
            $data['status'] = $request->is_available ? 'available' : 'unavailable';
            $data['created_by'] = auth()->id();

            $driver = Driver::create($data);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Chauffeur ajouté avec succès',
                'driver' => $driver
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de l\'ajout du chauffeur: ' . $e->getMessage());
            
            return response()->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de l\'ajout du chauffeur'
            ], 500);
        }
    }

    public function show(Driver $driver)
    {
        // Charger les voyages associés au chauffeur
        $driver->load(['trips' => function($query) {
            $query->with(['tripAssignments.cementOrderDetail', 'truck']);
        }]);
        return view('cement-manager.drivers.show', compact('driver'));
    }

    public function edit(Driver $driver)
    {
        return view('cement-manager.drivers.edit', compact('driver'));
    }

    public function update(Request $request, Driver $driver)
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone_number' => 'required|string|max:20|unique:drivers,phone_number,' . $driver->id,
            'license_number' => 'required|string|max:50|unique:drivers,license_number,' . $driver->id,
            'license_expiry' => 'required|date|after:today',
            'address' => 'required|string|max:500',
            'emergency_phone' => 'required|string|max:20',
            'is_available' => 'required|boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        DB::beginTransaction();
        try {
            $data = $request->all();
            $data['status'] = $request->is_available ? 'available' : 'unavailable';
            $data['updated_by'] = auth()->id();

            $driver->update($data);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Chauffeur mis à jour avec succès',
                'driver' => $driver
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de la mise à jour du chauffeur: ' . $e->getMessage());
            
            return response()->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de la mise à jour du chauffeur'
            ], 500);
        }
    }

    public function destroy(Driver $driver)
    {
        try {
            $driver->delete();
            return response()->json([
                'status' => 'success',
                'message' => 'Chauffeur supprimé avec succès'
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la suppression du chauffeur: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de la suppression du chauffeur'
            ], 500);
        }
    }
}
